menu "Example Configuration"

    config EXAMPLE_FATFS_MODE_READ_ONLY
        bool "Read only mode for generated FATFS image"
        default n
        help
            If read-only mode is set, the generated fatfs image will be raw (without wear levelling support).
            Otherwise it will support wear levelling that enables read-write mounting.

    config EXAMPLE_FATFS_WRITE_COUNT
        int "Number of volumes"
        default 1
        range 1 600
        help
            Number of writes to the file (for testing purposes).

    config EXAMPLE_FATFS_DEFAULT_DATETIME
        bool "Default modification date and time for generated FATFS image"
        default n
        help
            If default datetime is set, all files created in the generated FATFS partition have default time
            equal to FATFS origin time (1 January 1980)

endmenu
