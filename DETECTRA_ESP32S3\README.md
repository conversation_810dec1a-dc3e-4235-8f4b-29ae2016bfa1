# DETECTRA ESP32-S3 Object Detection Project

This project implements real-time object detection on ESP32-S3 using TensorFlow Lite Micro and camera input.

## Hardware Requirements

- ESP32-S3 development board with camera support
- Camera module (OV2640 or similar)
- MicroSD card (optional, for logging)

## Camera Pin Configuration

The project is configured for the following camera pin mapping:
```
PWDN_GPIO_NUM  = 9
RESET_GPIO_NUM = 11
XCLK_GPIO_NUM  = 8
SIOD_GPIO_NUM  = 13
SIOC_GPIO_NUM  = 3
Y2_GPIO_NUM    = 12  // D0
Y3_GPIO_NUM    = 7   // D1
Y4_GPIO_NUM    = 5   // D2
Y5_GPIO_NUM    = 4   // D3
Y6_GPIO_NUM    = 6   // D4
Y7_GPIO_NUM    = 15  // D5
Y8_GPIO_NUM    = 17  // D6
Y9_GPIO_NUM    = 18  // D7
VSYNC_GPIO_NUM = 10
HREF_GPIO_NUM  = 40
PCLK_GPIO_NUM  = 16
```

## Software Requirements

- ESP-IDF v5.0 or later
- Python 3.7+
- Git

## Setup Instructions

### 1. Install ESP-IDF

Follow the official ESP-IDF installation guide:
https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/

### 2. Clone Required Components

```bash
# Navigate to your project directory
cd DETECTRA_ESP32S3

# Add ESP32 Camera component
idf.py add-dependency "espressif/esp32-camera"

# Add TensorFlow Lite Micro (if available in component registry)
# idf.py add-dependency "espressif/tflite-lib"
```

### 3. Alternative: Manual Component Integration

If the components are not available via registry:

```bash
# Create components directory if it doesn't exist
mkdir -p components

# Clone ESP32 Camera
cd components
git clone https://github.com/espressif/esp32-camera.git

# For TensorFlow Lite Micro, you can use:
# - ESP-IDF's example component from examples/common_components/tflite-lib
# - Or integrate TensorFlow Lite Micro manually
```

### 4. Configure the Project

```bash
# Set target to ESP32-S3
idf.py set-target esp32s3

# Configure project (optional - defaults are provided)
idf.py menuconfig
```

### 5. Build and Flash

```bash
# Build the project
idf.py build

# Flash to device (replace PORT with your device port)
idf.py -p PORT flash monitor
```

## Model Integration

Your TensorFlow model (`epoch_100_int8_new.h`) has been integrated as `model_data.h`. The model details:
- Size: 1,121,344 bytes (~1.1MB)
- Format: TensorFlow Lite (quantized int8)
- Input: Likely 96x96x3 RGB images (adjust MODEL_INPUT_* in main.h if different)

## Project Structure

```
DETECTRA_ESP32S3/
├── CMakeLists.txt              # Main build configuration
├── sdkconfig.defaults          # ESP32-S3 configuration
├── partitions.csv              # Flash partition table
├── main/
│   ├── CMakeLists.txt          # Main component build config
│   ├── main.cpp                # Main application
│   ├── camera_handler.cpp      # Camera operations
│   ├── model_inference.cpp     # TensorFlow Lite inference
│   ├── image_processor.cpp     # Image preprocessing
│   └── include/
│       ├── main.h              # Main configuration and pin definitions
│       ├── camera_handler.h    # Camera interface
│       ├── model_inference.h   # Model interface
│       ├── image_processor.h   # Image processing interface
│       └── model_data.h        # Your trained model data
└── components/                 # External components
    ├── esp32-camera/           # Camera driver component
    └── tflite-micro/           # TensorFlow Lite Micro component
```

## Configuration

### Camera Settings
- Frame size: 96x96 (matches model input)
- Pixel format: RGB565
- Frame buffer: 2 buffers in PSRAM

### Model Settings
- Input size: 96x96x3 RGB
- Detection threshold: 0.5
- NMS threshold: 0.4
- Max detections: 10

### Memory Configuration
- PSRAM enabled for camera buffers and model
- Tensor arena: 60KB (adjust in model_inference.cpp if needed)

## Troubleshooting

### Build Issues
1. **TensorFlow Lite Micro not found**: 
   - Use ESP-IDF's tflite-lib component or integrate manually
   - Check components/tflite-micro/CMakeLists.txt

2. **Camera component not found**:
   - Run `idf.py add-dependency "espressif/esp32-camera"`
   - Or clone manually to components/esp32-camera

3. **Memory issues**:
   - Ensure PSRAM is enabled in sdkconfig
   - Adjust tensor arena size if needed

### Runtime Issues
1. **Camera initialization fails**:
   - Check pin connections match your hardware
   - Verify camera module compatibility

2. **Model inference fails**:
   - Check tensor arena size
   - Verify model input dimensions

3. **Out of memory**:
   - Enable PSRAM
   - Reduce frame buffer count
   - Optimize tensor arena size

## Performance Optimization

1. **Inference Speed**:
   - Use quantized models (int8)
   - Optimize tensor arena size
   - Consider model pruning

2. **Memory Usage**:
   - Use PSRAM for large buffers
   - Minimize frame buffer count
   - Optimize image preprocessing

3. **Power Consumption**:
   - Adjust camera frame rate
   - Use sleep modes when idle
   - Optimize CPU frequency

## Next Steps

1. Test camera functionality
2. Verify model inference
3. Tune detection thresholds
4. Add result visualization
5. Implement wireless connectivity for monitoring
6. Add data logging capabilities

## License

This project is provided as-is for educational and development purposes.
