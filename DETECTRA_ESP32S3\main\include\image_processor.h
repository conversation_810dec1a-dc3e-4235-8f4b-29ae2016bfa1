#ifndef IMAGE_PROCESSOR_H
#define IMAGE_PROCESSOR_H

#include <stdint.h>
#include "esp_err.h"
#include "main.h"

// Image processing functions
esp_err_t preprocess_image_for_model(uint8_t* input_image, int input_width, int input_height,
                                    uint8_t* output_buffer, int output_width, int output_height);

esp_err_t normalize_image(uint8_t* image_data, float* normalized_data, int width, int height, int channels);

esp_err_t resize_bilinear(uint8_t* src, int src_width, int src_height,
                         uint8_t* dst, int dst_width, int dst_height, int channels);

// Utility functions for image format conversion
esp_err_t rgb565_to_rgb888(uint16_t* src, uint8_t* dst, int pixel_count);
esp_err_t rgb888_to_grayscale(uint8_t* src, uint8_t* dst, int pixel_count);

#endif // IMAGE_PROCESSOR_H
