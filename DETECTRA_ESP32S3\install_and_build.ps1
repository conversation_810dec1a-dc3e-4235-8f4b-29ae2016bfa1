# ESP32-S3 Object Detection Project Setup Script
# PowerShell version

Write-Host "========================================" -ForegroundColor Green
Write-Host "ESP32-S3 Object Detection Project Setup" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if ESP-IDF is already installed
$idfExists = Get-Command "idf.py" -ErrorAction SilentlyContinue
if ($idfExists) {
    Write-Host "ESP-IDF is already installed!" -ForegroundColor Green
    goto BuildProject
}

Write-Host "ESP-IDF not found. Installing..." -ForegroundColor Yellow
Write-Host ""

# Check if Git is available
$gitExists = Get-Command "git" -ErrorAction SilentlyContinue
if (-not $gitExists) {
    Write-Host "Error: Git is not installed" -ForegroundColor Red
    Write-Host "Please install Git first: https://git-scm.com/download/win" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Create ESP-IDF directory
$espDir = "C:\esp"
if (-not (Test-Path $espDir)) {
    New-Item -ItemType Directory -Path $espDir -Force
}
Set-Location $espDir

Write-Host "Downloading ESP-IDF..." -ForegroundColor Yellow
try {
    & git clone --recursive https://github.com/espressif/esp-idf.git
    if ($LASTEXITCODE -ne 0) {
        throw "Git clone failed"
    }
} catch {
    Write-Host "Error: Failed to clone ESP-IDF repository" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Set-Location "esp-idf"

Write-Host "Installing ESP-IDF tools..." -ForegroundColor Yellow
try {
    & .\install.bat
    if ($LASTEXITCODE -ne 0) {
        throw "Install failed"
    }
} catch {
    Write-Host "Error: Failed to install ESP-IDF tools" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Setting up ESP-IDF environment..." -ForegroundColor Yellow
& .\export.bat

:BuildProject
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Building ESP32-S3 Object Detection Project" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Navigate to project directory
Set-Location "d:\OBJECT_DETECTION\ESP_PROJECTS\DETECTRA_ESP32S3"

Write-Host "Setting target to ESP32-S3..." -ForegroundColor Yellow
try {
    & idf.py set-target esp32s3
    if ($LASTEXITCODE -ne 0) {
        throw "Set target failed"
    }
} catch {
    Write-Host "Error: Failed to set target" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Adding ESP32 Camera dependency..." -ForegroundColor Yellow
try {
    & idf.py add-dependency "espressif/esp32-camera"
} catch {
    Write-Host "Warning: Failed to add camera dependency (continuing anyway)" -ForegroundColor Yellow
}

Write-Host "Building project..." -ForegroundColor Yellow
try {
    & idf.py build
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
} catch {
    Write-Host "Error: Build failed" -ForegroundColor Red
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Yellow
    Write-Host "1. Make sure ESP-IDF is properly installed"
    Write-Host "2. Check that all dependencies are available"
    Write-Host "3. Try running: idf.py clean && idf.py build"
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Build Successful!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Your ESP32-S3 object detection project has been built successfully." -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Connect your ESP32-S3 board with OV2640 camera"
Write-Host "2. Find your COM port (Device Manager)"
Write-Host "3. Flash the firmware: idf.py -p COMx flash monitor"
Write-Host ""
Write-Host "Replace COMx with your actual COM port (e.g., COM3)" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to exit"
