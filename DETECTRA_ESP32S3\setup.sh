#!/bin/bash
# Setup script for DETECTRA ESP32-S3 Object Detection Project

echo "Setting up DETECTRA ESP32-S3 Project..."

# Set target to ESP32-S3
echo "Setting target to ESP32-S3..."
idf.py set-target esp32s3

# Add required components
echo "Adding ESP32 Camera component..."
idf.py add-dependency "espressif/esp32-camera"

# Optional: Add TensorFlow Lite component if available
# idf.py add-dependency "espressif/tflite-lib"

echo "Setup complete!"
echo ""
echo "Next steps:"
echo "1. Connect your ESP32-S3 board with camera"
echo "2. Run: idf.py build"
echo "3. Run: idf.py -p [PORT] flash monitor"
echo ""
echo "Replace [PORT] with your device port (e.g., /dev/ttyUSB0 on Linux)"
