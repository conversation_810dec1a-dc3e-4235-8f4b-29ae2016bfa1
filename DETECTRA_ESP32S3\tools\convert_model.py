#!/usr/bin/env python3
"""
Model Conversion Utility for ESP32-S3 Object Detection

This script converts TensorFlow models to TensorFlow Lite format
and generates C++ header files for embedding in ESP32-S3 projects.

Usage:
    python convert_model.py --input model.h5 --output model_data.h
    python convert_model.py --input model.tflite --output model_data.h
"""

import argparse
import os
import sys
import numpy as np

try:
    import tensorflow as tf
except ImportError:
    print("TensorFlow not found. Install with: pip install tensorflow")
    sys.exit(1)

def convert_h5_to_tflite(h5_path, tflite_path, quantize=True):
    """Convert H5 model to TensorFlow Lite format."""
    print(f"Loading model from {h5_path}...")
    model = tf.keras.models.load_model(h5_path)
    
    # Convert to TensorFlow Lite
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    
    if quantize:
        print("Applying post-training quantization...")
        converter.optimizations = [tf.lite.Optimize.DEFAULT]
        converter.target_spec.supported_types = [tf.int8]
        converter.inference_input_type = tf.int8
        converter.inference_output_type = tf.int8
        
        # Representative dataset for quantization (you may need to customize this)
        def representative_data_gen():
            for _ in range(100):
                # Generate random data matching your model's input shape
                # Adjust shape based on your model
                yield [np.random.random((1, 96, 96, 3)).astype(np.float32)]
        
        converter.representative_dataset = representative_data_gen
    
    print("Converting to TensorFlow Lite...")
    tflite_model = converter.convert()
    
    # Save TensorFlow Lite model
    with open(tflite_path, 'wb') as f:
        f.write(tflite_model)
    
    print(f"TensorFlow Lite model saved to {tflite_path}")
    return tflite_model

def tflite_to_c_header(tflite_path, header_path, array_name="model_data"):
    """Convert TensorFlow Lite model to C++ header file."""
    print(f"Converting {tflite_path} to C++ header...")
    
    # Read TensorFlow Lite model
    with open(tflite_path, 'rb') as f:
        tflite_data = f.read()
    
    # Generate C++ header content
    header_content = f"""// Generated from {os.path.basename(tflite_path)}
// File size: {len(tflite_data)} bytes

#ifndef {array_name.upper()}_H
#define {array_name.upper()}_H

const unsigned char {array_name}[] = {{
"""
    
    # Add hex data
    for i, byte in enumerate(tflite_data):
        if i % 12 == 0:
            header_content += "\n  "
        header_content += f"0x{byte:02x}, "
    
    # Remove trailing comma and space
    header_content = header_content.rstrip(", ")
    
    header_content += f"""
}};
const unsigned int {array_name}_len = {len(tflite_data)};

#endif // {array_name.upper()}_H
"""
    
    # Write header file
    with open(header_path, 'w') as f:
        f.write(header_content)
    
    print(f"C++ header saved to {header_path}")
    print(f"Model size: {len(tflite_data)} bytes")

def main():
    parser = argparse.ArgumentParser(description="Convert models for ESP32-S3 deployment")
    parser.add_argument("--input", "-i", required=True, help="Input model file (.h5 or .tflite)")
    parser.add_argument("--output", "-o", required=True, help="Output C++ header file (.h)")
    parser.add_argument("--array-name", default="model_data", help="C++ array name (default: model_data)")
    parser.add_argument("--no-quantize", action="store_true", help="Skip quantization for H5 models")
    
    args = parser.parse_args()
    
    input_path = args.input
    output_path = args.output
    array_name = args.array_name
    
    if not os.path.exists(input_path):
        print(f"Error: Input file {input_path} not found")
        sys.exit(1)
    
    # Determine input file type
    if input_path.endswith('.h5'):
        # Convert H5 to TensorFlow Lite first
        tflite_path = input_path.replace('.h5', '.tflite')
        convert_h5_to_tflite(input_path, tflite_path, quantize=not args.no_quantize)
        input_path = tflite_path
    elif not input_path.endswith('.tflite'):
        print("Error: Input file must be .h5 or .tflite")
        sys.exit(1)
    
    # Convert TensorFlow Lite to C++ header
    tflite_to_c_header(input_path, output_path, array_name)
    
    print("\nConversion complete!")
    print(f"Include the header in your ESP32-S3 project: #include \"{os.path.basename(output_path)}\"")
    print(f"Use the model data: tflite::GetModel({array_name})")

if __name__ == "__main__":
    main()
