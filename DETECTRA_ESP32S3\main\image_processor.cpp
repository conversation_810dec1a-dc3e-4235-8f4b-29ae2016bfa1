#include "image_processor.h"
#include "esp_log.h"
#include <math.h>

static const char *TAG = "IMAGE_PROCESSOR";

esp_err_t preprocess_image_for_model(uint8_t* input_image, int input_width, int input_height,
                                    uint8_t* output_buffer, int output_width, int output_height)
{
    if (!input_image || !output_buffer) {
        ESP_LOGE(TAG, "Invalid input parameters");
        return ESP_ERR_INVALID_ARG;
    }
    
    // For RGB565 input format, convert to RGB888 first
    uint8_t* rgb888_buffer = (uint8_t*)malloc(input_width * input_height * 3);
    if (!rgb888_buffer) {
        ESP_LOGE(TAG, "Failed to allocate RGB888 buffer");
        return ESP_ERR_NO_MEM;
    }
    
    // Convert RGB565 to RGB888
    esp_err_t ret = rgb565_to_rgb888((uint16_t*)input_image, rgb888_buffer, input_width * input_height);
    if (ret != ESP_OK) {
        free(rgb888_buffer);
        return ret;
    }
    
    // Resize image if needed
    if (input_width != output_width || input_height != output_height) {
        ret = resize_bilinear(rgb888_buffer, input_width, input_height,
                             output_buffer, output_width, output_height, 3);
    } else {
        memcpy(output_buffer, rgb888_buffer, output_width * output_height * 3);
        ret = ESP_OK;
    }
    
    free(rgb888_buffer);
    return ret;
}

esp_err_t normalize_image(uint8_t* image_data, float* normalized_data, int width, int height, int channels)
{
    if (!image_data || !normalized_data) {
        return ESP_ERR_INVALID_ARG;
    }
    
    int total_pixels = width * height * channels;
    
    // Normalize pixel values from [0, 255] to [0, 1] or [-1, 1] depending on model requirements
    for (int i = 0; i < total_pixels; i++) {
        // Normalize to [0, 1] range
        normalized_data[i] = (float)image_data[i] / 255.0f;
        
        // Uncomment below for [-1, 1] normalization if your model requires it
        // normalized_data[i] = ((float)image_data[i] / 255.0f) * 2.0f - 1.0f;
    }
    
    return ESP_OK;
}

esp_err_t resize_bilinear(uint8_t* src, int src_width, int src_height,
                         uint8_t* dst, int dst_width, int dst_height, int channels)
{
    if (!src || !dst) {
        return ESP_ERR_INVALID_ARG;
    }
    
    float x_ratio = (float)src_width / dst_width;
    float y_ratio = (float)src_height / dst_height;
    
    for (int y = 0; y < dst_height; y++) {
        for (int x = 0; x < dst_width; x++) {
            float src_x = x * x_ratio;
            float src_y = y * y_ratio;
            
            int x1 = (int)src_x;
            int y1 = (int)src_y;
            int x2 = x1 + 1;
            int y2 = y1 + 1;
            
            // Clamp coordinates
            x2 = (x2 >= src_width) ? src_width - 1 : x2;
            y2 = (y2 >= src_height) ? src_height - 1 : y2;
            
            float dx = src_x - x1;
            float dy = src_y - y1;
            
            for (int c = 0; c < channels; c++) {
                // Get pixel values
                uint8_t p11 = src[(y1 * src_width + x1) * channels + c];
                uint8_t p12 = src[(y1 * src_width + x2) * channels + c];
                uint8_t p21 = src[(y2 * src_width + x1) * channels + c];
                uint8_t p22 = src[(y2 * src_width + x2) * channels + c];
                
                // Bilinear interpolation
                float interpolated = p11 * (1 - dx) * (1 - dy) +
                                   p12 * dx * (1 - dy) +
                                   p21 * (1 - dx) * dy +
                                   p22 * dx * dy;
                
                dst[(y * dst_width + x) * channels + c] = (uint8_t)interpolated;
            }
        }
    }
    
    return ESP_OK;
}

esp_err_t rgb565_to_rgb888(uint16_t* src, uint8_t* dst, int pixel_count)
{
    if (!src || !dst) {
        return ESP_ERR_INVALID_ARG;
    }
    
    for (int i = 0; i < pixel_count; i++) {
        uint16_t pixel = src[i];
        
        // Extract RGB components from RGB565
        uint8_t r = (pixel >> 11) & 0x1F;
        uint8_t g = (pixel >> 5) & 0x3F;
        uint8_t b = pixel & 0x1F;
        
        // Convert to 8-bit values
        dst[i * 3 + 0] = (r << 3) | (r >> 2);  // Red
        dst[i * 3 + 1] = (g << 2) | (g >> 4);  // Green
        dst[i * 3 + 2] = (b << 3) | (b >> 2);  // Blue
    }
    
    return ESP_OK;
}

esp_err_t rgb888_to_grayscale(uint8_t* src, uint8_t* dst, int pixel_count)
{
    if (!src || !dst) {
        return ESP_ERR_INVALID_ARG;
    }
    
    for (int i = 0; i < pixel_count; i++) {
        uint8_t r = src[i * 3 + 0];
        uint8_t g = src[i * 3 + 1];
        uint8_t b = src[i * 3 + 2];
        
        // Convert to grayscale using standard weights
        dst[i] = (uint8_t)(0.299f * r + 0.587f * g + 0.114f * b);
    }
    
    return ESP_OK;
}
