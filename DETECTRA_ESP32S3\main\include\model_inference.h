#ifndef MODEL_INFERENCE_H
#define MODEL_INFERENCE_H

#include "tensorflow/lite/micro/all_ops_resolver.h"
#include "tensorflow/lite/micro/micro_error_reporter.h"
#include "tensorflow/lite/micro/micro_interpreter.h"
#include "tensorflow/lite/schema/schema_generated.h"
#include "tensorflow/lite/version.h"
#include "esp_log.h"
#include "main.h"

// Detection result structure
typedef struct {
    float x;
    float y;
    float width;
    float height;
    float confidence;
    int class_id;
} detection_result_t;

// Model handler structure
typedef struct {
    const tflite::Model* model;
    tflite::MicroInterpreter* interpreter;
    tflite::MicroErrorReporter* error_reporter;
    tflite::AllOpsResolver* resolver;
    TfLiteTensor* input;
    TfLiteTensor* output;
    uint8_t* tensor_arena;
    bool initialized;
} model_handler_t;

// Function declarations
esp_err_t model_handler_init(void);
esp_err_t model_run_inference(uint8_t* input_data, detection_result_t* results, int* num_detections);
esp_err_t model_deinit(void);

// Utility functions
void print_detection_results(detection_result_t* results, int num_detections);
esp_err_t apply_nms(detection_result_t* detections, int* num_detections, float nms_threshold);

#endif // MODEL_INFERENCE_H
