#include "model_inference.h"
#include "model_data.h"  // This will contain your converted model

static const char *TAG = "MODEL_INFERENCE";

// Global model handler
static model_handler_t model_handler = {0};

// Tensor arena size - adjust based on your model requirements
constexpr int kTensorArenaSize = 60 * 1024;  // 60KB - adjust as needed

esp_err_t model_handler_init(void)
{
    if (model_handler.initialized) {
        ESP_LOGW(TAG, "Model already initialized");
        return ESP_OK;
    }
    
    // Allocate tensor arena
    model_handler.tensor_arena = (uint8_t*)heap_caps_malloc(kTensorArenaSize, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
    if (!model_handler.tensor_arena) {
        ESP_LOGE(TAG, "Failed to allocate tensor arena");
        return ESP_ERR_NO_MEM;
    }
    
    // Initialize error reporter
    static tflite::MicroErrorReporter micro_error_reporter;
    model_handler.error_reporter = &micro_error_reporter;
    
    // Load model from model_data.h (you'll need to generate this)
    model_handler.model = tflite::GetModel(g_model_data);
    if (model_handler.model->version() != TFLITE_SCHEMA_VERSION) {
        ESP_LOGE(TAG, "Model provided is schema version %d not equal to supported version %d",
                 model_handler.model->version(), TFLITE_SCHEMA_VERSION);
        return ESP_FAIL;
    }
    
    // Initialize ops resolver
    static tflite::AllOpsResolver resolver;
    model_handler.resolver = &resolver;
    
    // Build interpreter
    static tflite::MicroInterpreter static_interpreter(
        model_handler.model, *model_handler.resolver,
        model_handler.tensor_arena, kTensorArenaSize,
        model_handler.error_reporter);
    model_handler.interpreter = &static_interpreter;
    
    // Allocate tensors
    TfLiteStatus allocate_status = model_handler.interpreter->AllocateTensors();
    if (allocate_status != kTfLiteOk) {
        ESP_LOGE(TAG, "AllocateTensors() failed");
        return ESP_FAIL;
    }
    
    // Get input and output tensors
    model_handler.input = model_handler.interpreter->input(0);
    model_handler.output = model_handler.interpreter->output(0);
    
    // Verify input tensor dimensions
    if (model_handler.input->dims->size != 4) {
        ESP_LOGE(TAG, "Expected 4D input tensor, got %dD", model_handler.input->dims->size);
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Model input shape: [%d, %d, %d, %d]",
             model_handler.input->dims->data[0],
             model_handler.input->dims->data[1],
             model_handler.input->dims->data[2],
             model_handler.input->dims->data[3]);
    
    ESP_LOGI(TAG, "Model output shape: [%d, %d]",
             model_handler.output->dims->data[0],
             model_handler.output->dims->data[1]);
    
    model_handler.initialized = true;
    ESP_LOGI(TAG, "Model initialized successfully");
    
    return ESP_OK;
}

esp_err_t model_run_inference(uint8_t* input_data, detection_result_t* results, int* num_detections)
{
    if (!model_handler.initialized) {
        ESP_LOGE(TAG, "Model not initialized");
        return ESP_FAIL;
    }
    
    if (!input_data || !results || !num_detections) {
        ESP_LOGE(TAG, "Invalid parameters");
        return ESP_ERR_INVALID_ARG;
    }
    
    // Copy input data to input tensor
    int input_size = model_handler.input->bytes;
    
    if (model_handler.input->type == kTfLiteFloat32) {
        // Convert uint8 to float32 and normalize
        float* input_tensor = model_handler.input->data.f;
        for (int i = 0; i < input_size / sizeof(float); i++) {
            input_tensor[i] = (float)input_data[i] / 255.0f;
        }
    } else if (model_handler.input->type == kTfLiteUInt8) {
        // Direct copy for uint8 input
        memcpy(model_handler.input->data.uint8, input_data, input_size);
    } else {
        ESP_LOGE(TAG, "Unsupported input tensor type");
        return ESP_FAIL;
    }
    
    // Run inference
    TfLiteStatus invoke_status = model_handler.interpreter->Invoke();
    if (invoke_status != kTfLiteOk) {
        ESP_LOGE(TAG, "Invoke failed");
        return ESP_FAIL;
    }
    
    // Process output (this depends on your specific model output format)
    // This is a generic example - you'll need to adapt based on your model
    float* output_data = model_handler.output->data.f;
    int output_size = model_handler.output->dims->data[1];
    
    *num_detections = 0;
    
    // Example: assuming output format is [batch, detections, 6] where 6 = [x, y, w, h, confidence, class]
    // Adjust this based on your actual model output format
    for (int i = 0; i < output_size && *num_detections < 10; i += 6) {
        float confidence = output_data[i + 4];
        
        if (confidence > DETECTION_THRESHOLD) {
            results[*num_detections].x = output_data[i];
            results[*num_detections].y = output_data[i + 1];
            results[*num_detections].width = output_data[i + 2];
            results[*num_detections].height = output_data[i + 3];
            results[*num_detections].confidence = confidence;
            results[*num_detections].class_id = (int)output_data[i + 5];
            (*num_detections)++;
        }
    }
    
    // Apply Non-Maximum Suppression if needed
    if (*num_detections > 1) {
        apply_nms(results, num_detections, NMS_THRESHOLD);
    }
    
    return ESP_OK;
}

esp_err_t model_deinit(void)
{
    if (model_handler.tensor_arena) {
        free(model_handler.tensor_arena);
        model_handler.tensor_arena = NULL;
    }
    
    model_handler.initialized = false;
    ESP_LOGI(TAG, "Model deinitialized");
    
    return ESP_OK;
}

void print_detection_results(detection_result_t* results, int num_detections)
{
    if (num_detections == 0) {
        ESP_LOGI(TAG, "No objects detected");
        return;
    }
    
    ESP_LOGI(TAG, "Detected %d objects:", num_detections);
    for (int i = 0; i < num_detections; i++) {
        ESP_LOGI(TAG, "  Object %d: class=%d, confidence=%.2f, bbox=(%.2f,%.2f,%.2f,%.2f)",
                 i, results[i].class_id, results[i].confidence,
                 results[i].x, results[i].y, results[i].width, results[i].height);
    }
}

esp_err_t apply_nms(detection_result_t* detections, int* num_detections, float nms_threshold)
{
    if (!detections || !num_detections || *num_detections <= 1) {
        return ESP_OK;
    }
    
    // Simple NMS implementation
    bool suppressed[10] = {false}; // Max 10 detections
    int original_count = *num_detections;
    
    for (int i = 0; i < original_count; i++) {
        if (suppressed[i]) continue;
        
        for (int j = i + 1; j < original_count; j++) {
            if (suppressed[j]) continue;
            
            // Calculate IoU
            float x1 = fmax(detections[i].x, detections[j].x);
            float y1 = fmax(detections[i].y, detections[j].y);
            float x2 = fmin(detections[i].x + detections[i].width, 
                           detections[j].x + detections[j].width);
            float y2 = fmin(detections[i].y + detections[i].height, 
                           detections[j].y + detections[j].height);
            
            if (x2 > x1 && y2 > y1) {
                float intersection = (x2 - x1) * (y2 - y1);
                float area1 = detections[i].width * detections[i].height;
                float area2 = detections[j].width * detections[j].height;
                float union_area = area1 + area2 - intersection;
                float iou = intersection / union_area;
                
                if (iou > nms_threshold) {
                    // Suppress the detection with lower confidence
                    if (detections[i].confidence > detections[j].confidence) {
                        suppressed[j] = true;
                    } else {
                        suppressed[i] = true;
                        break;
                    }
                }
            }
        }
    }
    
    // Compact the array
    int write_idx = 0;
    for (int i = 0; i < original_count; i++) {
        if (!suppressed[i]) {
            if (write_idx != i) {
                detections[write_idx] = detections[i];
            }
            write_idx++;
        }
    }
    
    *num_detections = write_idx;
    return ESP_OK;
}
