#ifndef MAIN_H
#define MAIN_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_log.h"
#include "esp_camera.h"
#include "esp_timer.h"
#include "nvs_flash.h"

// Project configuration
#define CAMERA_MODEL_ESP32S3_EYE
#define BOARD_ESP32S3_EYE

// Camera pins for your ESP32-S3 board
#define CAM_PIN_PWDN    9
#define CAM_PIN_RESET   11
#define CAM_PIN_XCLK    8
#define CAM_PIN_SIOD    13
#define CAM_PIN_SIOC    3
#define CAM_PIN_D7      18  // Y9
#define CAM_PIN_D6      17  // Y8
#define CAM_PIN_D5      15  // Y7
#define CAM_PIN_D4      6   // Y6
#define CAM_PIN_D3      4   // Y5
#define CAM_PIN_D2      5   // Y4
#define CAM_PIN_D1      7   // Y3
#define CAM_PIN_D0      12  // Y2
#define CAM_PIN_VSYNC   10
#define CAM_PIN_HREF    40
#define CAM_PIN_PCLK    16

// Model configuration
#define MODEL_INPUT_WIDTH  96
#define MODEL_INPUT_HEIGHT 96
#define MODEL_INPUT_CHANNELS 3

// Detection thresholds
#define DETECTION_THRESHOLD 0.5f
#define NMS_THRESHOLD 0.4f

// Function declarations
void camera_init(void);
void model_init(void);
void detection_task(void *pvParameters);

#endif // MAIN_H
