#!/usr/bin/env python3
"""
Test script to verify ESP32-S3 Object Detection project structure
and validate configuration before building.
"""

import os
import sys

def check_file_exists(filepath, description):
    """Check if a file exists and report status."""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        print(f"✅ {description}: {filepath} ({size:,} bytes)")
        return True
    else:
        print(f"❌ {description}: {filepath} (NOT FOUND)")
        return False

def check_directory_exists(dirpath, description):
    """Check if a directory exists and report status."""
    if os.path.exists(dirpath) and os.path.isdir(dirpath):
        files = len(os.listdir(dirpath))
        print(f"✅ {description}: {dirpath} ({files} items)")
        return True
    else:
        print(f"❌ {description}: {dirpath} (NOT FOUND)")
        return False

def validate_model_file():
    """Validate the model data file."""
    model_file = "main/include/model_data.h"
    if not os.path.exists(model_file):
        return False
    
    with open(model_file, 'r') as f:
        content = f.read()
        
    # Check for expected model variables
    if "epoch_100_int8" in content and "epoch_100_int8_len" in content:
        print("✅ Model variables found in model_data.h")
        return True
    else:
        print("❌ Model variables not found in model_data.h")
        return False

def validate_pin_configuration():
    """Validate camera pin configuration."""
    main_h = "main/include/main.h"
    if not os.path.exists(main_h):
        return False
    
    with open(main_h, 'r') as f:
        content = f.read()
    
    # Check for your specific pin configuration
    expected_pins = {
        "CAM_PIN_PWDN": "9",
        "CAM_PIN_RESET": "11", 
        "CAM_PIN_XCLK": "8",
        "CAM_PIN_SIOD": "13",
        "CAM_PIN_SIOC": "3"
    }
    
    all_found = True
    for pin_name, expected_value in expected_pins.items():
        if f"#define {pin_name}    {expected_value}" in content:
            print(f"✅ Pin {pin_name} = GPIO {expected_value}")
        else:
            print(f"❌ Pin {pin_name} not configured correctly")
            all_found = False
    
    return all_found

def main():
    """Main test function."""
    print("🔍 ESP32-S3 Object Detection Project Validation")
    print("=" * 50)
    
    # Change to project directory
    project_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_dir)
    print(f"📁 Project directory: {project_dir}")
    print()
    
    # Test counters
    passed = 0
    total = 0
    
    # Check main project files
    tests = [
        ("CMakeLists.txt", "Main build file"),
        ("sdkconfig.defaults", "ESP32-S3 configuration"),
        ("partitions.csv", "Memory partition table"),
        ("README.md", "Project documentation"),
        ("SETUP_GUIDE.md", "Setup guide"),
    ]
    
    print("📋 Main Project Files:")
    for filepath, description in tests:
        total += 1
        if check_file_exists(filepath, description):
            passed += 1
    print()
    
    # Check main source directory
    print("📂 Source Code Structure:")
    main_tests = [
        ("main", "Main source directory"),
        ("main/include", "Header files directory"),
        ("components", "Components directory"),
    ]
    
    for dirpath, description in main_tests:
        total += 1
        if check_directory_exists(dirpath, description):
            passed += 1
    print()
    
    # Check source files
    print("📄 Source Files:")
    source_tests = [
        ("main/main.cpp", "Main application"),
        ("main/camera_handler_simple.cpp", "Camera handler"),
        ("main/model_inference_simple.cpp", "Model inference"),
        ("main/image_processor.cpp", "Image processor"),
        ("main/CMakeLists.txt", "Main component build file"),
    ]
    
    for filepath, description in source_tests:
        total += 1
        if check_file_exists(filepath, description):
            passed += 1
    print()
    
    # Check header files
    print("📋 Header Files:")
    header_tests = [
        ("main/include/main.h", "Main configuration"),
        ("main/include/camera_handler.h", "Camera interface"),
        ("main/include/model_inference.h", "Model interface"),
        ("main/include/image_processor.h", "Image processing interface"),
        ("main/include/model_data.h", "Trained model data"),
    ]
    
    for filepath, description in header_tests:
        total += 1
        if check_file_exists(filepath, description):
            passed += 1
    print()
    
    # Validate model integration
    print("🧠 Model Integration:")
    total += 1
    if validate_model_file():
        passed += 1
    print()
    
    # Validate pin configuration
    print("📌 Pin Configuration:")
    total += 1
    if validate_pin_configuration():
        passed += 1
    print()
    
    # Check build scripts
    print("🔧 Build Scripts:")
    script_tests = [
        ("install_and_build.bat", "Windows build script"),
        ("install_and_build.ps1", "PowerShell build script"),
        ("setup.bat", "Windows setup script"),
        ("setup.sh", "Linux setup script"),
    ]
    
    for filepath, description in script_tests:
        total += 1
        if check_file_exists(filepath, description):
            passed += 1
    print()
    
    # Final report
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Project is ready for building.")
        print()
        print("Next steps:")
        print("1. Install ESP-IDF: Run install_and_build.bat")
        print("2. Or manually install ESP-IDF from: https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/")
        print("3. Build project: idf.py build")
        print("4. Flash to ESP32-S3: idf.py -p COMx flash monitor")
        return 0
    else:
        print(f"❌ {total - passed} tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
