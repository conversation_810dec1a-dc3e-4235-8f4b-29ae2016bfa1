@echo off
echo ========================================
echo ESP32-S3 Object Detection Project Setup
echo ========================================
echo.

REM Check if ESP-IDF is already installed
where idf.py >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo ESP-IDF is already installed!
    goto :build_project
)

echo ESP-IDF not found. Installing...
echo.

REM Create ESP-IDF directory
if not exist "C:\esp" mkdir "C:\esp"
cd /d "C:\esp"

echo Downloading ESP-IDF...
git clone --recursive https://github.com/espressif/esp-idf.git
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to clone ESP-IDF repository
    echo Please install Git first: https://git-scm.com/download/win
    pause
    exit /b 1
)

cd esp-idf

echo Installing ESP-IDF tools...
install.bat
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to install ESP-IDF tools
    pause
    exit /b 1
)

echo Setting up ESP-IDF environment...
call export.bat

:build_project
echo.
echo ========================================
echo Building ESP32-S3 Object Detection Project
echo ========================================
echo.

REM Navigate to project directory
cd /d "d:\OBJECT_DETECTION\ESP_PROJECTS\DETECTRA_ESP32S3"

echo Setting target to ESP32-S3...
idf.py set-target esp32s3
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to set target
    pause
    exit /b 1
)

echo Adding ESP32 Camera dependency...
idf.py add-dependency "espressif/esp32-camera"
if %ERRORLEVEL% neq 0 (
    echo Warning: Failed to add camera dependency (will continue anyway)
)

echo Configuring project...
idf.py menuconfig --non-interactive
if %ERRORLEVEL% neq 0 (
    echo Warning: Failed to configure project (will continue anyway)
)

echo Building project...
idf.py build
if %ERRORLEVEL% neq 0 (
    echo Error: Build failed
    echo.
    echo Common solutions:
    echo 1. Make sure ESP-IDF is properly installed
    echo 2. Check that all dependencies are available
    echo 3. Try running: idf.py clean && idf.py build
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build Successful!
echo ========================================
echo.
echo Your ESP32-S3 object detection project has been built successfully.
echo.
echo Next steps:
echo 1. Connect your ESP32-S3 board with OV2640 camera
echo 2. Find your COM port (Device Manager)
echo 3. Flash the firmware: idf.py -p COMx flash monitor
echo.
echo Replace COMx with your actual COM port (e.g., COM3)
echo.
pause
