
set(PYTHON "C:/Users/<USER>/.espressif/python_env/idf6.0_py3.9_env/Scripts/python.exe")
set(__BUILD_PROPERTIES "PYTHON;__BUILD_PROPERTIES;IDF_PATH;__PREFIX;__CHECK_PYTHON;IDF_COMPONENT_MANAGER;COMPILE_DEFINITIONS;COMPILE_OPTIONS;C_COMPILE_OPTIONS;CXX_COMPILE_OPTIONS;__COMPONENT_TARGETS;BUILD_COMPONENT_DIRS;BUILD_COMPONENT_TARGETS;__COMPONENT_REQUIRES_COMMON;IDF_VER;__ROOT_KCONFIG;__ROOT_SDKCONFIG_RENAME;__OUTPUT_SDKCONFIG;EXTRA_CMAKE_ARGS;__COMPONENT_MANAGER_INTERFACE_VERSION;BOOTLOADER_BUILD;NON_OS_BUILD;ESP_TEE_BUILD;IDF_TOOLCHAIN;IDF_TARGET;IDF_TARGET_ARCH;PROJECT_DIR;PROJECT_NAME;PROJECT_VER;BUILD_DIR;SDKCONFIG;SDKCONFIG_DEFAULTS")
set(IDF_PATH "C:/esp/esp-idf")
set(__PREFIX "idf")
set(__CHECK_PYTHON "0")
set(IDF_COMPONENT_MANAGER "1")
set(COMPILE_DEFINITIONS "_GLIBCXX_USE_POSIX_SEMAPHORE;_GLIBCXX_HAVE_POSIX_SEMAPHORE;_GNU_SOURCE;IDF_VER="v6.0-dev-725-g9ddef27ed4-dirty"")
set(COMPILE_OPTIONS "-ffunction-sections;-fdata-sections;-Wall;-Werror;-Wno-error=unused-function;-Wno-error=unused-variable;-Wno-error=unused-but-set-variable;-Wno-error=deprecated-declarations;-Wextra;-Wno-error=extra;-Wno-unused-parameter;-Wno-sign-compare;-Wno-enum-conversion;-gdwarf-4;-ggdb")
set(C_COMPILE_OPTIONS "-std=gnu17")
set(CXX_COMPILE_OPTIONS "-std=gnu++2b")
set(__COMPONENT_TARGETS "___idf_app_trace;___idf_app_update;___idf_bootloader;___idf_bootloader_support;___idf_bt;___idf_cmock;___idf_console;___idf_cxx;___idf_driver;___idf_efuse;___idf_esp-tls;___idf_esp_adc;___idf_esp_app_format;___idf_esp_bootloader_format;___idf_esp_coex;___idf_esp_common;___idf_esp_driver_ana_cmpr;___idf_esp_driver_bitscrambler;___idf_esp_driver_cam;___idf_esp_driver_dac;___idf_esp_driver_gpio;___idf_esp_driver_gptimer;___idf_esp_driver_i2c;___idf_esp_driver_i2s;___idf_esp_driver_isp;___idf_esp_driver_jpeg;___idf_esp_driver_ledc;___idf_esp_driver_mcpwm;___idf_esp_driver_parlio;___idf_esp_driver_pcnt;___idf_esp_driver_ppa;___idf_esp_driver_rmt;___idf_esp_driver_sd_intf;___idf_esp_driver_sdio;___idf_esp_driver_sdm;___idf_esp_driver_sdmmc;___idf_esp_driver_sdspi;___idf_esp_driver_spi;___idf_esp_driver_touch_sens;___idf_esp_driver_tsens;___idf_esp_driver_twai;___idf_esp_driver_uart;___idf_esp_driver_usb_serial_jtag;___idf_esp_eth;___idf_esp_event;___idf_esp_gdbstub;___idf_esp_hid;___idf_esp_http_client;___idf_esp_http_server;___idf_esp_https_ota;___idf_esp_https_server;___idf_esp_hw_support;___idf_esp_lcd;___idf_esp_local_ctrl;___idf_esp_mm;___idf_esp_netif;___idf_esp_netif_stack;___idf_esp_partition;___idf_esp_phy;___idf_esp_pm;___idf_esp_psram;___idf_esp_ringbuf;___idf_esp_rom;___idf_esp_security;___idf_esp_system;___idf_esp_tee;___idf_esp_timer;___idf_esp_vfs_console;___idf_esp_wifi;___idf_espcoredump;___idf_esptool_py;___idf_fatfs;___idf_freertos;___idf_hal;___idf_heap;___idf_http_parser;___idf_idf_test;___idf_ieee802154;___idf_json;___idf_linux;___idf_log;___idf_lwip;___idf_mbedtls;___idf_mqtt;___idf_newlib;___idf_nvs_flash;___idf_nvs_sec_provider;___idf_openthread;___idf_partition_table;___idf_perfmon;___idf_protobuf-c;___idf_protocomm;___idf_pthread;___idf_riscv;___idf_rt;___idf_sdmmc;___idf_soc;___idf_spi_flash;___idf_spiffs;___idf_tcp_transport;___idf_touch_element;___idf_ulp;___idf_unity;___idf_usb;___idf_vfs;___idf_wear_levelling;___idf_wifi_provisioning;___idf_wpa_supplicant;___idf_xtensa;___idf_main;___idf_esp32-camera;___idf_tflite-micro")
set(BUILD_COMPONENT_DIRS "C:/esp/esp-idf/components/app_trace;C:/esp/esp-idf/components/app_update;C:/esp/esp-idf/components/bootloader;C:/esp/esp-idf/components/bootloader_support;C:/esp/esp-idf/components/bt;C:/esp/esp-idf/components/cmock;C:/esp/esp-idf/components/console;C:/esp/esp-idf/components/cxx;C:/esp/esp-idf/components/driver;C:/esp/esp-idf/components/efuse;C:/esp/esp-idf/components/esp-tls;C:/esp/esp-idf/components/esp_adc;C:/esp/esp-idf/components/esp_app_format;C:/esp/esp-idf/components/esp_bootloader_format;C:/esp/esp-idf/components/esp_coex;C:/esp/esp-idf/components/esp_common;C:/esp/esp-idf/components/esp_driver_ana_cmpr;C:/esp/esp-idf/components/esp_driver_bitscrambler;C:/esp/esp-idf/components/esp_driver_cam;C:/esp/esp-idf/components/esp_driver_dac;C:/esp/esp-idf/components/esp_driver_gpio;C:/esp/esp-idf/components/esp_driver_gptimer;C:/esp/esp-idf/components/esp_driver_i2c;C:/esp/esp-idf/components/esp_driver_i2s;C:/esp/esp-idf/components/esp_driver_isp;C:/esp/esp-idf/components/esp_driver_jpeg;C:/esp/esp-idf/components/esp_driver_ledc;C:/esp/esp-idf/components/esp_driver_mcpwm;C:/esp/esp-idf/components/esp_driver_parlio;C:/esp/esp-idf/components/esp_driver_pcnt;C:/esp/esp-idf/components/esp_driver_ppa;C:/esp/esp-idf/components/esp_driver_rmt;C:/esp/esp-idf/components/esp_driver_sd_intf;C:/esp/esp-idf/components/esp_driver_sdio;C:/esp/esp-idf/components/esp_driver_sdm;C:/esp/esp-idf/components/esp_driver_sdmmc;C:/esp/esp-idf/components/esp_driver_sdspi;C:/esp/esp-idf/components/esp_driver_spi;C:/esp/esp-idf/components/esp_driver_touch_sens;C:/esp/esp-idf/components/esp_driver_tsens;C:/esp/esp-idf/components/esp_driver_twai;C:/esp/esp-idf/components/esp_driver_uart;C:/esp/esp-idf/components/esp_driver_usb_serial_jtag;C:/esp/esp-idf/components/esp_eth;C:/esp/esp-idf/components/esp_event;C:/esp/esp-idf/components/esp_gdbstub;C:/esp/esp-idf/components/esp_hid;C:/esp/esp-idf/components/esp_http_client;C:/esp/esp-idf/components/esp_http_server;C:/esp/esp-idf/components/esp_https_ota;C:/esp/esp-idf/components/esp_https_server;C:/esp/esp-idf/components/esp_hw_support;C:/esp/esp-idf/components/esp_lcd;C:/esp/esp-idf/components/esp_local_ctrl;C:/esp/esp-idf/components/esp_mm;C:/esp/esp-idf/components/esp_netif;C:/esp/esp-idf/components/esp_netif_stack;C:/esp/esp-idf/components/esp_partition;C:/esp/esp-idf/components/esp_phy;C:/esp/esp-idf/components/esp_pm;C:/esp/esp-idf/components/esp_psram;C:/esp/esp-idf/components/esp_ringbuf;C:/esp/esp-idf/components/esp_rom;C:/esp/esp-idf/components/esp_security;C:/esp/esp-idf/components/esp_system;C:/esp/esp-idf/components/esp_tee;C:/esp/esp-idf/components/esp_timer;C:/esp/esp-idf/components/esp_vfs_console;C:/esp/esp-idf/components/esp_wifi;C:/esp/esp-idf/components/espcoredump;C:/esp/esp-idf/components/esptool_py;C:/esp/esp-idf/components/fatfs;C:/esp/esp-idf/components/freertos;C:/esp/esp-idf/components/hal;C:/esp/esp-idf/components/heap;C:/esp/esp-idf/components/http_parser;C:/esp/esp-idf/components/idf_test;C:/esp/esp-idf/components/ieee802154;C:/esp/esp-idf/components/json;C:/esp/esp-idf/components/linux;C:/esp/esp-idf/components/log;C:/esp/esp-idf/components/lwip;C:/esp/esp-idf/components/mbedtls;C:/esp/esp-idf/components/mqtt;C:/esp/esp-idf/components/newlib;C:/esp/esp-idf/components/nvs_flash;C:/esp/esp-idf/components/nvs_sec_provider;C:/esp/esp-idf/components/openthread;C:/esp/esp-idf/components/partition_table;C:/esp/esp-idf/components/perfmon;C:/esp/esp-idf/components/protobuf-c;C:/esp/esp-idf/components/protocomm;C:/esp/esp-idf/components/pthread;C:/esp/esp-idf/components/riscv;C:/esp/esp-idf/components/rt;C:/esp/esp-idf/components/sdmmc;C:/esp/esp-idf/components/soc;C:/esp/esp-idf/components/spi_flash;C:/esp/esp-idf/components/spiffs;C:/esp/esp-idf/components/tcp_transport;C:/esp/esp-idf/components/touch_element;C:/esp/esp-idf/components/ulp;C:/esp/esp-idf/components/unity;C:/esp/esp-idf/components/usb;C:/esp/esp-idf/components/vfs;C:/esp/esp-idf/components/wear_levelling;C:/esp/esp-idf/components/wifi_provisioning;C:/esp/esp-idf/components/wpa_supplicant;C:/esp/esp-idf/components/xtensa;D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/main;D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/components/esp32-camera;D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/components/tflite-micro")
set(BUILD_COMPONENT_TARGETS "___idf_app_trace;___idf_app_update;___idf_bootloader;___idf_bootloader_support;___idf_bt;___idf_cmock;___idf_console;___idf_cxx;___idf_driver;___idf_efuse;___idf_esp-tls;___idf_esp_adc;___idf_esp_app_format;___idf_esp_bootloader_format;___idf_esp_coex;___idf_esp_common;___idf_esp_driver_ana_cmpr;___idf_esp_driver_bitscrambler;___idf_esp_driver_cam;___idf_esp_driver_dac;___idf_esp_driver_gpio;___idf_esp_driver_gptimer;___idf_esp_driver_i2c;___idf_esp_driver_i2s;___idf_esp_driver_isp;___idf_esp_driver_jpeg;___idf_esp_driver_ledc;___idf_esp_driver_mcpwm;___idf_esp_driver_parlio;___idf_esp_driver_pcnt;___idf_esp_driver_ppa;___idf_esp_driver_rmt;___idf_esp_driver_sd_intf;___idf_esp_driver_sdio;___idf_esp_driver_sdm;___idf_esp_driver_sdmmc;___idf_esp_driver_sdspi;___idf_esp_driver_spi;___idf_esp_driver_touch_sens;___idf_esp_driver_tsens;___idf_esp_driver_twai;___idf_esp_driver_uart;___idf_esp_driver_usb_serial_jtag;___idf_esp_eth;___idf_esp_event;___idf_esp_gdbstub;___idf_esp_hid;___idf_esp_http_client;___idf_esp_http_server;___idf_esp_https_ota;___idf_esp_https_server;___idf_esp_hw_support;___idf_esp_lcd;___idf_esp_local_ctrl;___idf_esp_mm;___idf_esp_netif;___idf_esp_netif_stack;___idf_esp_partition;___idf_esp_phy;___idf_esp_pm;___idf_esp_psram;___idf_esp_ringbuf;___idf_esp_rom;___idf_esp_security;___idf_esp_system;___idf_esp_tee;___idf_esp_timer;___idf_esp_vfs_console;___idf_esp_wifi;___idf_espcoredump;___idf_esptool_py;___idf_fatfs;___idf_freertos;___idf_hal;___idf_heap;___idf_http_parser;___idf_idf_test;___idf_ieee802154;___idf_json;___idf_linux;___idf_log;___idf_lwip;___idf_mbedtls;___idf_mqtt;___idf_newlib;___idf_nvs_flash;___idf_nvs_sec_provider;___idf_openthread;___idf_partition_table;___idf_perfmon;___idf_protobuf-c;___idf_protocomm;___idf_pthread;___idf_riscv;___idf_rt;___idf_sdmmc;___idf_soc;___idf_spi_flash;___idf_spiffs;___idf_tcp_transport;___idf_touch_element;___idf_ulp;___idf_unity;___idf_usb;___idf_vfs;___idf_wear_levelling;___idf_wifi_provisioning;___idf_wpa_supplicant;___idf_xtensa;___idf_main;___idf_esp32-camera;___idf_tflite-micro")
set(__COMPONENT_REQUIRES_COMMON "cxx;newlib;freertos;esp_hw_support;heap;log;soc;hal;esp_rom;esp_common;esp_system;xtensa")
set(IDF_VER "v6.0-dev-725-g9ddef27ed4-dirty")
set(__ROOT_KCONFIG "C:/esp/esp-idf/Kconfig")
set(__ROOT_SDKCONFIG_RENAME "C:/esp/esp-idf/sdkconfig.rename")
set(__OUTPUT_SDKCONFIG "1")
set(EXTRA_CMAKE_ARGS "")
set(__COMPONENT_MANAGER_INTERFACE_VERSION "4")
set(BOOTLOADER_BUILD "")
set(NON_OS_BUILD "")
set(ESP_TEE_BUILD "")
set(IDF_TOOLCHAIN "gcc")
set(IDF_TARGET "esp32s3")
set(IDF_TARGET_ARCH "xtensa")
set(PROJECT_DIR "D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3")
set(PROJECT_NAME "detectra_esp32s3")
set(PROJECT_VER "1")
set(BUILD_DIR "D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/build")
set(SDKCONFIG "D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/sdkconfig")
set(SDKCONFIG_DEFAULTS "D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/sdkconfig.defaults")