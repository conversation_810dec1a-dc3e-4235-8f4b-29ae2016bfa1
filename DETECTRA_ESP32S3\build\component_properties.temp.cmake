
set(__component____idf_app_trace_COMPONENT_LIB "__idf_app_trace")
set(__component____idf_app_trace___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_app_trace_COMPONENT_NAME "app_trace")
set(__component____idf_app_trace_COMPONENT_DIR "C:/esp/esp-idf/components/app_trace")
set(__component____idf_app_trace_COMPONENT_ALIAS "idf::app_trace")
set(__component____idf_app_trace_COMPONENT_SOURCE "idf_components")
set(__component____idf_app_trace___PREFIX "idf")
set(__component____idf_app_trace_KCONFIG "C:/esp/esp-idf/components/app_trace/Kconfig")
set(__component____idf_app_trace_KCONFIG_PROJBUILD "")
set(__component____idf_app_trace_SDKCONFIG_RENAME "C:/esp/esp-idf/components/app_trace/sdkconfig.rename")
set(__component____idf_app_update_COMPONENT_LIB "__idf_app_update")
set(__component____idf_app_update___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_app_update_COMPONENT_NAME "app_update")
set(__component____idf_app_update_COMPONENT_DIR "C:/esp/esp-idf/components/app_update")
set(__component____idf_app_update_COMPONENT_ALIAS "idf::app_update")
set(__component____idf_app_update_COMPONENT_SOURCE "idf_components")
set(__component____idf_app_update___PREFIX "idf")
set(__component____idf_app_update_KCONFIG "")
set(__component____idf_app_update_KCONFIG_PROJBUILD "")
set(__component____idf_app_update_SDKCONFIG_RENAME "")
set(__component____idf_bootloader_COMPONENT_LIB "__idf_bootloader")
set(__component____idf_bootloader___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_bootloader_COMPONENT_NAME "bootloader")
set(__component____idf_bootloader_COMPONENT_DIR "C:/esp/esp-idf/components/bootloader")
set(__component____idf_bootloader_COMPONENT_ALIAS "idf::bootloader")
set(__component____idf_bootloader_COMPONENT_SOURCE "idf_components")
set(__component____idf_bootloader___PREFIX "idf")
set(__component____idf_bootloader_KCONFIG "")
set(__component____idf_bootloader_KCONFIG_PROJBUILD "C:/esp/esp-idf/components/bootloader/Kconfig.projbuild")
set(__component____idf_bootloader_SDKCONFIG_RENAME "C:/esp/esp-idf/components/bootloader/sdkconfig.rename")
set(__component____idf_bootloader_support_COMPONENT_LIB "__idf_bootloader_support")
set(__component____idf_bootloader_support___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_bootloader_support_COMPONENT_NAME "bootloader_support")
set(__component____idf_bootloader_support_COMPONENT_DIR "C:/esp/esp-idf/components/bootloader_support")
set(__component____idf_bootloader_support_COMPONENT_ALIAS "idf::bootloader_support")
set(__component____idf_bootloader_support_COMPONENT_SOURCE "idf_components")
set(__component____idf_bootloader_support___PREFIX "idf")
set(__component____idf_bootloader_support_KCONFIG "")
set(__component____idf_bootloader_support_KCONFIG_PROJBUILD "")
set(__component____idf_bootloader_support_SDKCONFIG_RENAME "")
set(__component____idf_bt_COMPONENT_LIB "__idf_bt")
set(__component____idf_bt___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_bt_COMPONENT_NAME "bt")
set(__component____idf_bt_COMPONENT_DIR "C:/esp/esp-idf/components/bt")
set(__component____idf_bt_COMPONENT_ALIAS "idf::bt")
set(__component____idf_bt_COMPONENT_SOURCE "idf_components")
set(__component____idf_bt___PREFIX "idf")
set(__component____idf_bt_KCONFIG "C:/esp/esp-idf/components/bt/Kconfig")
set(__component____idf_bt_KCONFIG_PROJBUILD "")
set(__component____idf_bt_SDKCONFIG_RENAME "C:/esp/esp-idf/components/bt/sdkconfig.rename;C:/esp/esp-idf/components/bt/sdkconfig.rename.esp32s3")
set(__component____idf_cmock_COMPONENT_LIB "__idf_cmock")
set(__component____idf_cmock___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_cmock_COMPONENT_NAME "cmock")
set(__component____idf_cmock_COMPONENT_DIR "C:/esp/esp-idf/components/cmock")
set(__component____idf_cmock_COMPONENT_ALIAS "idf::cmock")
set(__component____idf_cmock_COMPONENT_SOURCE "idf_components")
set(__component____idf_cmock___PREFIX "idf")
set(__component____idf_cmock_KCONFIG "")
set(__component____idf_cmock_KCONFIG_PROJBUILD "")
set(__component____idf_cmock_SDKCONFIG_RENAME "")
set(__component____idf_console_COMPONENT_LIB "__idf_console")
set(__component____idf_console___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_console_COMPONENT_NAME "console")
set(__component____idf_console_COMPONENT_DIR "C:/esp/esp-idf/components/console")
set(__component____idf_console_COMPONENT_ALIAS "idf::console")
set(__component____idf_console_COMPONENT_SOURCE "idf_components")
set(__component____idf_console___PREFIX "idf")
set(__component____idf_console_KCONFIG "C:/esp/esp-idf/components/console/Kconfig")
set(__component____idf_console_KCONFIG_PROJBUILD "")
set(__component____idf_console_SDKCONFIG_RENAME "")
set(__component____idf_cxx_COMPONENT_LIB "__idf_cxx")
set(__component____idf_cxx___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_cxx_COMPONENT_NAME "cxx")
set(__component____idf_cxx_COMPONENT_DIR "C:/esp/esp-idf/components/cxx")
set(__component____idf_cxx_COMPONENT_ALIAS "idf::cxx")
set(__component____idf_cxx_COMPONENT_SOURCE "idf_components")
set(__component____idf_cxx___PREFIX "idf")
set(__component____idf_cxx_KCONFIG "")
set(__component____idf_cxx_KCONFIG_PROJBUILD "")
set(__component____idf_cxx_SDKCONFIG_RENAME "")
set(__component____idf_driver_COMPONENT_LIB "__idf_driver")
set(__component____idf_driver___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_driver_COMPONENT_NAME "driver")
set(__component____idf_driver_COMPONENT_DIR "C:/esp/esp-idf/components/driver")
set(__component____idf_driver_COMPONENT_ALIAS "idf::driver")
set(__component____idf_driver_COMPONENT_SOURCE "idf_components")
set(__component____idf_driver___PREFIX "idf")
set(__component____idf_driver_KCONFIG "C:/esp/esp-idf/components/driver/Kconfig")
set(__component____idf_driver_KCONFIG_PROJBUILD "")
set(__component____idf_driver_SDKCONFIG_RENAME "C:/esp/esp-idf/components/driver/sdkconfig.rename")
set(__component____idf_efuse_COMPONENT_LIB "__idf_efuse")
set(__component____idf_efuse___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_efuse_COMPONENT_NAME "efuse")
set(__component____idf_efuse_COMPONENT_DIR "C:/esp/esp-idf/components/efuse")
set(__component____idf_efuse_COMPONENT_ALIAS "idf::efuse")
set(__component____idf_efuse_COMPONENT_SOURCE "idf_components")
set(__component____idf_efuse___PREFIX "idf")
set(__component____idf_efuse_KCONFIG "C:/esp/esp-idf/components/efuse/Kconfig")
set(__component____idf_efuse_KCONFIG_PROJBUILD "")
set(__component____idf_efuse_SDKCONFIG_RENAME "")
set(__component____idf_esp-tls_COMPONENT_LIB "__idf_esp-tls")
set(__component____idf_esp-tls___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp-tls_COMPONENT_NAME "esp-tls")
set(__component____idf_esp-tls_COMPONENT_DIR "C:/esp/esp-idf/components/esp-tls")
set(__component____idf_esp-tls_COMPONENT_ALIAS "idf::esp-tls")
set(__component____idf_esp-tls_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp-tls___PREFIX "idf")
set(__component____idf_esp-tls_KCONFIG "C:/esp/esp-idf/components/esp-tls/Kconfig")
set(__component____idf_esp-tls_KCONFIG_PROJBUILD "")
set(__component____idf_esp-tls_SDKCONFIG_RENAME "")
set(__component____idf_esp_adc_COMPONENT_LIB "__idf_esp_adc")
set(__component____idf_esp_adc___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_adc_COMPONENT_NAME "esp_adc")
set(__component____idf_esp_adc_COMPONENT_DIR "C:/esp/esp-idf/components/esp_adc")
set(__component____idf_esp_adc_COMPONENT_ALIAS "idf::esp_adc")
set(__component____idf_esp_adc_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_adc___PREFIX "idf")
set(__component____idf_esp_adc_KCONFIG "C:/esp/esp-idf/components/esp_adc/Kconfig")
set(__component____idf_esp_adc_KCONFIG_PROJBUILD "")
set(__component____idf_esp_adc_SDKCONFIG_RENAME "")
set(__component____idf_esp_app_format_COMPONENT_LIB "__idf_esp_app_format")
set(__component____idf_esp_app_format___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_app_format_COMPONENT_NAME "esp_app_format")
set(__component____idf_esp_app_format_COMPONENT_DIR "C:/esp/esp-idf/components/esp_app_format")
set(__component____idf_esp_app_format_COMPONENT_ALIAS "idf::esp_app_format")
set(__component____idf_esp_app_format_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_app_format___PREFIX "idf")
set(__component____idf_esp_app_format_KCONFIG "")
set(__component____idf_esp_app_format_KCONFIG_PROJBUILD "C:/esp/esp-idf/components/esp_app_format/Kconfig.projbuild")
set(__component____idf_esp_app_format_SDKCONFIG_RENAME "")
set(__component____idf_esp_bootloader_format_COMPONENT_LIB "__idf_esp_bootloader_format")
set(__component____idf_esp_bootloader_format___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_bootloader_format_COMPONENT_NAME "esp_bootloader_format")
set(__component____idf_esp_bootloader_format_COMPONENT_DIR "C:/esp/esp-idf/components/esp_bootloader_format")
set(__component____idf_esp_bootloader_format_COMPONENT_ALIAS "idf::esp_bootloader_format")
set(__component____idf_esp_bootloader_format_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_bootloader_format___PREFIX "idf")
set(__component____idf_esp_bootloader_format_KCONFIG "")
set(__component____idf_esp_bootloader_format_KCONFIG_PROJBUILD "")
set(__component____idf_esp_bootloader_format_SDKCONFIG_RENAME "")
set(__component____idf_esp_coex_COMPONENT_LIB "__idf_esp_coex")
set(__component____idf_esp_coex___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_coex_COMPONENT_NAME "esp_coex")
set(__component____idf_esp_coex_COMPONENT_DIR "C:/esp/esp-idf/components/esp_coex")
set(__component____idf_esp_coex_COMPONENT_ALIAS "idf::esp_coex")
set(__component____idf_esp_coex_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_coex___PREFIX "idf")
set(__component____idf_esp_coex_KCONFIG "C:/esp/esp-idf/components/esp_coex/Kconfig")
set(__component____idf_esp_coex_KCONFIG_PROJBUILD "")
set(__component____idf_esp_coex_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_coex/sdkconfig.rename")
set(__component____idf_esp_common_COMPONENT_LIB "__idf_esp_common")
set(__component____idf_esp_common___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_common_COMPONENT_NAME "esp_common")
set(__component____idf_esp_common_COMPONENT_DIR "C:/esp/esp-idf/components/esp_common")
set(__component____idf_esp_common_COMPONENT_ALIAS "idf::esp_common")
set(__component____idf_esp_common_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_common___PREFIX "idf")
set(__component____idf_esp_common_KCONFIG "C:/esp/esp-idf/components/esp_common/Kconfig")
set(__component____idf_esp_common_KCONFIG_PROJBUILD "")
set(__component____idf_esp_common_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_ana_cmpr_COMPONENT_LIB "__idf_esp_driver_ana_cmpr")
set(__component____idf_esp_driver_ana_cmpr___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_ana_cmpr_COMPONENT_NAME "esp_driver_ana_cmpr")
set(__component____idf_esp_driver_ana_cmpr_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_ana_cmpr")
set(__component____idf_esp_driver_ana_cmpr_COMPONENT_ALIAS "idf::esp_driver_ana_cmpr")
set(__component____idf_esp_driver_ana_cmpr_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_ana_cmpr___PREFIX "idf")
set(__component____idf_esp_driver_ana_cmpr_KCONFIG "C:/esp/esp-idf/components/esp_driver_ana_cmpr/Kconfig")
set(__component____idf_esp_driver_ana_cmpr_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_ana_cmpr_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_driver_ana_cmpr/sdkconfig.rename")
set(__component____idf_esp_driver_bitscrambler_COMPONENT_LIB "__idf_esp_driver_bitscrambler")
set(__component____idf_esp_driver_bitscrambler___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_bitscrambler_COMPONENT_NAME "esp_driver_bitscrambler")
set(__component____idf_esp_driver_bitscrambler_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_bitscrambler")
set(__component____idf_esp_driver_bitscrambler_COMPONENT_ALIAS "idf::esp_driver_bitscrambler")
set(__component____idf_esp_driver_bitscrambler_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_bitscrambler___PREFIX "idf")
set(__component____idf_esp_driver_bitscrambler_KCONFIG "C:/esp/esp-idf/components/esp_driver_bitscrambler/Kconfig")
set(__component____idf_esp_driver_bitscrambler_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_bitscrambler_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_cam_COMPONENT_LIB "__idf_esp_driver_cam")
set(__component____idf_esp_driver_cam___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_cam_COMPONENT_NAME "esp_driver_cam")
set(__component____idf_esp_driver_cam_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_cam")
set(__component____idf_esp_driver_cam_COMPONENT_ALIAS "idf::esp_driver_cam")
set(__component____idf_esp_driver_cam_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_cam___PREFIX "idf")
set(__component____idf_esp_driver_cam_KCONFIG "C:/esp/esp-idf/components/esp_driver_cam/Kconfig")
set(__component____idf_esp_driver_cam_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_cam_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_driver_cam/sdkconfig.rename")
set(__component____idf_esp_driver_dac_COMPONENT_LIB "__idf_esp_driver_dac")
set(__component____idf_esp_driver_dac___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_dac_COMPONENT_NAME "esp_driver_dac")
set(__component____idf_esp_driver_dac_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_dac")
set(__component____idf_esp_driver_dac_COMPONENT_ALIAS "idf::esp_driver_dac")
set(__component____idf_esp_driver_dac_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_dac___PREFIX "idf")
set(__component____idf_esp_driver_dac_KCONFIG "C:/esp/esp-idf/components/esp_driver_dac/Kconfig")
set(__component____idf_esp_driver_dac_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_dac_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_gpio_COMPONENT_LIB "__idf_esp_driver_gpio")
set(__component____idf_esp_driver_gpio___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_gpio_COMPONENT_NAME "esp_driver_gpio")
set(__component____idf_esp_driver_gpio_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_gpio")
set(__component____idf_esp_driver_gpio_COMPONENT_ALIAS "idf::esp_driver_gpio")
set(__component____idf_esp_driver_gpio_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_gpio___PREFIX "idf")
set(__component____idf_esp_driver_gpio_KCONFIG "C:/esp/esp-idf/components/esp_driver_gpio/Kconfig")
set(__component____idf_esp_driver_gpio_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_gpio_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_gptimer_COMPONENT_LIB "__idf_esp_driver_gptimer")
set(__component____idf_esp_driver_gptimer___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_gptimer_COMPONENT_NAME "esp_driver_gptimer")
set(__component____idf_esp_driver_gptimer_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_gptimer")
set(__component____idf_esp_driver_gptimer_COMPONENT_ALIAS "idf::esp_driver_gptimer")
set(__component____idf_esp_driver_gptimer_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_gptimer___PREFIX "idf")
set(__component____idf_esp_driver_gptimer_KCONFIG "C:/esp/esp-idf/components/esp_driver_gptimer/Kconfig")
set(__component____idf_esp_driver_gptimer_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_gptimer_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_driver_gptimer/sdkconfig.rename")
set(__component____idf_esp_driver_i2c_COMPONENT_LIB "__idf_esp_driver_i2c")
set(__component____idf_esp_driver_i2c___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_i2c_COMPONENT_NAME "esp_driver_i2c")
set(__component____idf_esp_driver_i2c_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_i2c")
set(__component____idf_esp_driver_i2c_COMPONENT_ALIAS "idf::esp_driver_i2c")
set(__component____idf_esp_driver_i2c_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_i2c___PREFIX "idf")
set(__component____idf_esp_driver_i2c_KCONFIG "C:/esp/esp-idf/components/esp_driver_i2c/Kconfig")
set(__component____idf_esp_driver_i2c_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_i2c_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_i2s_COMPONENT_LIB "__idf_esp_driver_i2s")
set(__component____idf_esp_driver_i2s___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_i2s_COMPONENT_NAME "esp_driver_i2s")
set(__component____idf_esp_driver_i2s_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_i2s")
set(__component____idf_esp_driver_i2s_COMPONENT_ALIAS "idf::esp_driver_i2s")
set(__component____idf_esp_driver_i2s_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_i2s___PREFIX "idf")
set(__component____idf_esp_driver_i2s_KCONFIG "C:/esp/esp-idf/components/esp_driver_i2s/Kconfig")
set(__component____idf_esp_driver_i2s_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_i2s_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_isp_COMPONENT_LIB "__idf_esp_driver_isp")
set(__component____idf_esp_driver_isp___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_isp_COMPONENT_NAME "esp_driver_isp")
set(__component____idf_esp_driver_isp_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_isp")
set(__component____idf_esp_driver_isp_COMPONENT_ALIAS "idf::esp_driver_isp")
set(__component____idf_esp_driver_isp_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_isp___PREFIX "idf")
set(__component____idf_esp_driver_isp_KCONFIG "C:/esp/esp-idf/components/esp_driver_isp/Kconfig")
set(__component____idf_esp_driver_isp_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_isp_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_jpeg_COMPONENT_LIB "__idf_esp_driver_jpeg")
set(__component____idf_esp_driver_jpeg___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_jpeg_COMPONENT_NAME "esp_driver_jpeg")
set(__component____idf_esp_driver_jpeg_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_jpeg")
set(__component____idf_esp_driver_jpeg_COMPONENT_ALIAS "idf::esp_driver_jpeg")
set(__component____idf_esp_driver_jpeg_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_jpeg___PREFIX "idf")
set(__component____idf_esp_driver_jpeg_KCONFIG "C:/esp/esp-idf/components/esp_driver_jpeg/Kconfig")
set(__component____idf_esp_driver_jpeg_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_jpeg_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_ledc_COMPONENT_LIB "__idf_esp_driver_ledc")
set(__component____idf_esp_driver_ledc___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_ledc_COMPONENT_NAME "esp_driver_ledc")
set(__component____idf_esp_driver_ledc_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_ledc")
set(__component____idf_esp_driver_ledc_COMPONENT_ALIAS "idf::esp_driver_ledc")
set(__component____idf_esp_driver_ledc_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_ledc___PREFIX "idf")
set(__component____idf_esp_driver_ledc_KCONFIG "C:/esp/esp-idf/components/esp_driver_ledc/Kconfig")
set(__component____idf_esp_driver_ledc_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_ledc_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_mcpwm_COMPONENT_LIB "__idf_esp_driver_mcpwm")
set(__component____idf_esp_driver_mcpwm___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_mcpwm_COMPONENT_NAME "esp_driver_mcpwm")
set(__component____idf_esp_driver_mcpwm_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_mcpwm")
set(__component____idf_esp_driver_mcpwm_COMPONENT_ALIAS "idf::esp_driver_mcpwm")
set(__component____idf_esp_driver_mcpwm_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_mcpwm___PREFIX "idf")
set(__component____idf_esp_driver_mcpwm_KCONFIG "C:/esp/esp-idf/components/esp_driver_mcpwm/Kconfig")
set(__component____idf_esp_driver_mcpwm_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_mcpwm_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_driver_mcpwm/sdkconfig.rename")
set(__component____idf_esp_driver_parlio_COMPONENT_LIB "__idf_esp_driver_parlio")
set(__component____idf_esp_driver_parlio___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_parlio_COMPONENT_NAME "esp_driver_parlio")
set(__component____idf_esp_driver_parlio_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_parlio")
set(__component____idf_esp_driver_parlio_COMPONENT_ALIAS "idf::esp_driver_parlio")
set(__component____idf_esp_driver_parlio_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_parlio___PREFIX "idf")
set(__component____idf_esp_driver_parlio_KCONFIG "C:/esp/esp-idf/components/esp_driver_parlio/Kconfig")
set(__component____idf_esp_driver_parlio_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_parlio_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_pcnt_COMPONENT_LIB "__idf_esp_driver_pcnt")
set(__component____idf_esp_driver_pcnt___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_pcnt_COMPONENT_NAME "esp_driver_pcnt")
set(__component____idf_esp_driver_pcnt_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_pcnt")
set(__component____idf_esp_driver_pcnt_COMPONENT_ALIAS "idf::esp_driver_pcnt")
set(__component____idf_esp_driver_pcnt_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_pcnt___PREFIX "idf")
set(__component____idf_esp_driver_pcnt_KCONFIG "C:/esp/esp-idf/components/esp_driver_pcnt/Kconfig")
set(__component____idf_esp_driver_pcnt_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_pcnt_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_ppa_COMPONENT_LIB "__idf_esp_driver_ppa")
set(__component____idf_esp_driver_ppa___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_ppa_COMPONENT_NAME "esp_driver_ppa")
set(__component____idf_esp_driver_ppa_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_ppa")
set(__component____idf_esp_driver_ppa_COMPONENT_ALIAS "idf::esp_driver_ppa")
set(__component____idf_esp_driver_ppa_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_ppa___PREFIX "idf")
set(__component____idf_esp_driver_ppa_KCONFIG "")
set(__component____idf_esp_driver_ppa_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_ppa_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_rmt_COMPONENT_LIB "__idf_esp_driver_rmt")
set(__component____idf_esp_driver_rmt___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_rmt_COMPONENT_NAME "esp_driver_rmt")
set(__component____idf_esp_driver_rmt_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_rmt")
set(__component____idf_esp_driver_rmt_COMPONENT_ALIAS "idf::esp_driver_rmt")
set(__component____idf_esp_driver_rmt_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_rmt___PREFIX "idf")
set(__component____idf_esp_driver_rmt_KCONFIG "C:/esp/esp-idf/components/esp_driver_rmt/Kconfig")
set(__component____idf_esp_driver_rmt_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_rmt_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_sd_intf_COMPONENT_LIB "__idf_esp_driver_sd_intf")
set(__component____idf_esp_driver_sd_intf___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_sd_intf_COMPONENT_NAME "esp_driver_sd_intf")
set(__component____idf_esp_driver_sd_intf_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_sd_intf")
set(__component____idf_esp_driver_sd_intf_COMPONENT_ALIAS "idf::esp_driver_sd_intf")
set(__component____idf_esp_driver_sd_intf_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_sd_intf___PREFIX "idf")
set(__component____idf_esp_driver_sd_intf_KCONFIG "")
set(__component____idf_esp_driver_sd_intf_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_sd_intf_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_sdio_COMPONENT_LIB "__idf_esp_driver_sdio")
set(__component____idf_esp_driver_sdio___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_sdio_COMPONENT_NAME "esp_driver_sdio")
set(__component____idf_esp_driver_sdio_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_sdio")
set(__component____idf_esp_driver_sdio_COMPONENT_ALIAS "idf::esp_driver_sdio")
set(__component____idf_esp_driver_sdio_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_sdio___PREFIX "idf")
set(__component____idf_esp_driver_sdio_KCONFIG "")
set(__component____idf_esp_driver_sdio_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_sdio_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_sdm_COMPONENT_LIB "__idf_esp_driver_sdm")
set(__component____idf_esp_driver_sdm___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_sdm_COMPONENT_NAME "esp_driver_sdm")
set(__component____idf_esp_driver_sdm_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_sdm")
set(__component____idf_esp_driver_sdm_COMPONENT_ALIAS "idf::esp_driver_sdm")
set(__component____idf_esp_driver_sdm_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_sdm___PREFIX "idf")
set(__component____idf_esp_driver_sdm_KCONFIG "C:/esp/esp-idf/components/esp_driver_sdm/Kconfig")
set(__component____idf_esp_driver_sdm_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_sdm_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_sdmmc_COMPONENT_LIB "__idf_esp_driver_sdmmc")
set(__component____idf_esp_driver_sdmmc___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_sdmmc_COMPONENT_NAME "esp_driver_sdmmc")
set(__component____idf_esp_driver_sdmmc_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_sdmmc")
set(__component____idf_esp_driver_sdmmc_COMPONENT_ALIAS "idf::esp_driver_sdmmc")
set(__component____idf_esp_driver_sdmmc_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_sdmmc___PREFIX "idf")
set(__component____idf_esp_driver_sdmmc_KCONFIG "C:/esp/esp-idf/components/esp_driver_sdmmc/Kconfig")
set(__component____idf_esp_driver_sdmmc_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_sdmmc_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_sdspi_COMPONENT_LIB "__idf_esp_driver_sdspi")
set(__component____idf_esp_driver_sdspi___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_sdspi_COMPONENT_NAME "esp_driver_sdspi")
set(__component____idf_esp_driver_sdspi_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_sdspi")
set(__component____idf_esp_driver_sdspi_COMPONENT_ALIAS "idf::esp_driver_sdspi")
set(__component____idf_esp_driver_sdspi_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_sdspi___PREFIX "idf")
set(__component____idf_esp_driver_sdspi_KCONFIG "")
set(__component____idf_esp_driver_sdspi_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_sdspi_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_spi_COMPONENT_LIB "__idf_esp_driver_spi")
set(__component____idf_esp_driver_spi___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_spi_COMPONENT_NAME "esp_driver_spi")
set(__component____idf_esp_driver_spi_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_spi")
set(__component____idf_esp_driver_spi_COMPONENT_ALIAS "idf::esp_driver_spi")
set(__component____idf_esp_driver_spi_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_spi___PREFIX "idf")
set(__component____idf_esp_driver_spi_KCONFIG "C:/esp/esp-idf/components/esp_driver_spi/Kconfig")
set(__component____idf_esp_driver_spi_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_spi_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_touch_sens_COMPONENT_LIB "__idf_esp_driver_touch_sens")
set(__component____idf_esp_driver_touch_sens___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_touch_sens_COMPONENT_NAME "esp_driver_touch_sens")
set(__component____idf_esp_driver_touch_sens_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_touch_sens")
set(__component____idf_esp_driver_touch_sens_COMPONENT_ALIAS "idf::esp_driver_touch_sens")
set(__component____idf_esp_driver_touch_sens_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_touch_sens___PREFIX "idf")
set(__component____idf_esp_driver_touch_sens_KCONFIG "C:/esp/esp-idf/components/esp_driver_touch_sens/Kconfig")
set(__component____idf_esp_driver_touch_sens_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_touch_sens_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_tsens_COMPONENT_LIB "__idf_esp_driver_tsens")
set(__component____idf_esp_driver_tsens___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_tsens_COMPONENT_NAME "esp_driver_tsens")
set(__component____idf_esp_driver_tsens_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_tsens")
set(__component____idf_esp_driver_tsens_COMPONENT_ALIAS "idf::esp_driver_tsens")
set(__component____idf_esp_driver_tsens_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_tsens___PREFIX "idf")
set(__component____idf_esp_driver_tsens_KCONFIG "C:/esp/esp-idf/components/esp_driver_tsens/Kconfig")
set(__component____idf_esp_driver_tsens_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_tsens_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_twai_COMPONENT_LIB "__idf_esp_driver_twai")
set(__component____idf_esp_driver_twai___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_twai_COMPONENT_NAME "esp_driver_twai")
set(__component____idf_esp_driver_twai_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_twai")
set(__component____idf_esp_driver_twai_COMPONENT_ALIAS "idf::esp_driver_twai")
set(__component____idf_esp_driver_twai_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_twai___PREFIX "idf")
set(__component____idf_esp_driver_twai_KCONFIG "C:/esp/esp-idf/components/esp_driver_twai/Kconfig")
set(__component____idf_esp_driver_twai_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_twai_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_uart_COMPONENT_LIB "__idf_esp_driver_uart")
set(__component____idf_esp_driver_uart___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_uart_COMPONENT_NAME "esp_driver_uart")
set(__component____idf_esp_driver_uart_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_uart")
set(__component____idf_esp_driver_uart_COMPONENT_ALIAS "idf::esp_driver_uart")
set(__component____idf_esp_driver_uart_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_uart___PREFIX "idf")
set(__component____idf_esp_driver_uart_KCONFIG "C:/esp/esp-idf/components/esp_driver_uart/Kconfig")
set(__component____idf_esp_driver_uart_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_uart_SDKCONFIG_RENAME "")
set(__component____idf_esp_driver_usb_serial_jtag_COMPONENT_LIB "__idf_esp_driver_usb_serial_jtag")
set(__component____idf_esp_driver_usb_serial_jtag___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_driver_usb_serial_jtag_COMPONENT_NAME "esp_driver_usb_serial_jtag")
set(__component____idf_esp_driver_usb_serial_jtag_COMPONENT_DIR "C:/esp/esp-idf/components/esp_driver_usb_serial_jtag")
set(__component____idf_esp_driver_usb_serial_jtag_COMPONENT_ALIAS "idf::esp_driver_usb_serial_jtag")
set(__component____idf_esp_driver_usb_serial_jtag_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_driver_usb_serial_jtag___PREFIX "idf")
set(__component____idf_esp_driver_usb_serial_jtag_KCONFIG "C:/esp/esp-idf/components/esp_driver_usb_serial_jtag/Kconfig")
set(__component____idf_esp_driver_usb_serial_jtag_KCONFIG_PROJBUILD "")
set(__component____idf_esp_driver_usb_serial_jtag_SDKCONFIG_RENAME "")
set(__component____idf_esp_eth_COMPONENT_LIB "__idf_esp_eth")
set(__component____idf_esp_eth___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_eth_COMPONENT_NAME "esp_eth")
set(__component____idf_esp_eth_COMPONENT_DIR "C:/esp/esp-idf/components/esp_eth")
set(__component____idf_esp_eth_COMPONENT_ALIAS "idf::esp_eth")
set(__component____idf_esp_eth_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_eth___PREFIX "idf")
set(__component____idf_esp_eth_KCONFIG "C:/esp/esp-idf/components/esp_eth/Kconfig")
set(__component____idf_esp_eth_KCONFIG_PROJBUILD "")
set(__component____idf_esp_eth_SDKCONFIG_RENAME "")
set(__component____idf_esp_event_COMPONENT_LIB "__idf_esp_event")
set(__component____idf_esp_event___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_event_COMPONENT_NAME "esp_event")
set(__component____idf_esp_event_COMPONENT_DIR "C:/esp/esp-idf/components/esp_event")
set(__component____idf_esp_event_COMPONENT_ALIAS "idf::esp_event")
set(__component____idf_esp_event_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_event___PREFIX "idf")
set(__component____idf_esp_event_KCONFIG "C:/esp/esp-idf/components/esp_event/Kconfig")
set(__component____idf_esp_event_KCONFIG_PROJBUILD "")
set(__component____idf_esp_event_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_event/sdkconfig.rename")
set(__component____idf_esp_gdbstub_COMPONENT_LIB "__idf_esp_gdbstub")
set(__component____idf_esp_gdbstub___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_gdbstub_COMPONENT_NAME "esp_gdbstub")
set(__component____idf_esp_gdbstub_COMPONENT_DIR "C:/esp/esp-idf/components/esp_gdbstub")
set(__component____idf_esp_gdbstub_COMPONENT_ALIAS "idf::esp_gdbstub")
set(__component____idf_esp_gdbstub_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_gdbstub___PREFIX "idf")
set(__component____idf_esp_gdbstub_KCONFIG "C:/esp/esp-idf/components/esp_gdbstub/Kconfig")
set(__component____idf_esp_gdbstub_KCONFIG_PROJBUILD "")
set(__component____idf_esp_gdbstub_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_gdbstub/sdkconfig.rename")
set(__component____idf_esp_hid_COMPONENT_LIB "__idf_esp_hid")
set(__component____idf_esp_hid___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_hid_COMPONENT_NAME "esp_hid")
set(__component____idf_esp_hid_COMPONENT_DIR "C:/esp/esp-idf/components/esp_hid")
set(__component____idf_esp_hid_COMPONENT_ALIAS "idf::esp_hid")
set(__component____idf_esp_hid_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_hid___PREFIX "idf")
set(__component____idf_esp_hid_KCONFIG "C:/esp/esp-idf/components/esp_hid/Kconfig")
set(__component____idf_esp_hid_KCONFIG_PROJBUILD "")
set(__component____idf_esp_hid_SDKCONFIG_RENAME "")
set(__component____idf_esp_http_client_COMPONENT_LIB "__idf_esp_http_client")
set(__component____idf_esp_http_client___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_http_client_COMPONENT_NAME "esp_http_client")
set(__component____idf_esp_http_client_COMPONENT_DIR "C:/esp/esp-idf/components/esp_http_client")
set(__component____idf_esp_http_client_COMPONENT_ALIAS "idf::esp_http_client")
set(__component____idf_esp_http_client_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_http_client___PREFIX "idf")
set(__component____idf_esp_http_client_KCONFIG "C:/esp/esp-idf/components/esp_http_client/Kconfig")
set(__component____idf_esp_http_client_KCONFIG_PROJBUILD "")
set(__component____idf_esp_http_client_SDKCONFIG_RENAME "")
set(__component____idf_esp_http_server_COMPONENT_LIB "__idf_esp_http_server")
set(__component____idf_esp_http_server___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_http_server_COMPONENT_NAME "esp_http_server")
set(__component____idf_esp_http_server_COMPONENT_DIR "C:/esp/esp-idf/components/esp_http_server")
set(__component____idf_esp_http_server_COMPONENT_ALIAS "idf::esp_http_server")
set(__component____idf_esp_http_server_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_http_server___PREFIX "idf")
set(__component____idf_esp_http_server_KCONFIG "C:/esp/esp-idf/components/esp_http_server/Kconfig")
set(__component____idf_esp_http_server_KCONFIG_PROJBUILD "")
set(__component____idf_esp_http_server_SDKCONFIG_RENAME "")
set(__component____idf_esp_https_ota_COMPONENT_LIB "__idf_esp_https_ota")
set(__component____idf_esp_https_ota___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_https_ota_COMPONENT_NAME "esp_https_ota")
set(__component____idf_esp_https_ota_COMPONENT_DIR "C:/esp/esp-idf/components/esp_https_ota")
set(__component____idf_esp_https_ota_COMPONENT_ALIAS "idf::esp_https_ota")
set(__component____idf_esp_https_ota_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_https_ota___PREFIX "idf")
set(__component____idf_esp_https_ota_KCONFIG "C:/esp/esp-idf/components/esp_https_ota/Kconfig")
set(__component____idf_esp_https_ota_KCONFIG_PROJBUILD "")
set(__component____idf_esp_https_ota_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_https_ota/sdkconfig.rename")
set(__component____idf_esp_https_server_COMPONENT_LIB "__idf_esp_https_server")
set(__component____idf_esp_https_server___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_https_server_COMPONENT_NAME "esp_https_server")
set(__component____idf_esp_https_server_COMPONENT_DIR "C:/esp/esp-idf/components/esp_https_server")
set(__component____idf_esp_https_server_COMPONENT_ALIAS "idf::esp_https_server")
set(__component____idf_esp_https_server_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_https_server___PREFIX "idf")
set(__component____idf_esp_https_server_KCONFIG "C:/esp/esp-idf/components/esp_https_server/Kconfig")
set(__component____idf_esp_https_server_KCONFIG_PROJBUILD "")
set(__component____idf_esp_https_server_SDKCONFIG_RENAME "")
set(__component____idf_esp_hw_support_COMPONENT_LIB "__idf_esp_hw_support")
set(__component____idf_esp_hw_support___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_hw_support_COMPONENT_NAME "esp_hw_support")
set(__component____idf_esp_hw_support_COMPONENT_DIR "C:/esp/esp-idf/components/esp_hw_support")
set(__component____idf_esp_hw_support_COMPONENT_ALIAS "idf::esp_hw_support")
set(__component____idf_esp_hw_support_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_hw_support___PREFIX "idf")
set(__component____idf_esp_hw_support_KCONFIG "C:/esp/esp-idf/components/esp_hw_support/Kconfig")
set(__component____idf_esp_hw_support_KCONFIG_PROJBUILD "")
set(__component____idf_esp_hw_support_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_hw_support/sdkconfig.rename;C:/esp/esp-idf/components/esp_hw_support/sdkconfig.rename.esp32s3")
set(__component____idf_esp_lcd_COMPONENT_LIB "__idf_esp_lcd")
set(__component____idf_esp_lcd___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_lcd_COMPONENT_NAME "esp_lcd")
set(__component____idf_esp_lcd_COMPONENT_DIR "C:/esp/esp-idf/components/esp_lcd")
set(__component____idf_esp_lcd_COMPONENT_ALIAS "idf::esp_lcd")
set(__component____idf_esp_lcd_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_lcd___PREFIX "idf")
set(__component____idf_esp_lcd_KCONFIG "C:/esp/esp-idf/components/esp_lcd/Kconfig")
set(__component____idf_esp_lcd_KCONFIG_PROJBUILD "")
set(__component____idf_esp_lcd_SDKCONFIG_RENAME "")
set(__component____idf_esp_local_ctrl_COMPONENT_LIB "__idf_esp_local_ctrl")
set(__component____idf_esp_local_ctrl___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_local_ctrl_COMPONENT_NAME "esp_local_ctrl")
set(__component____idf_esp_local_ctrl_COMPONENT_DIR "C:/esp/esp-idf/components/esp_local_ctrl")
set(__component____idf_esp_local_ctrl_COMPONENT_ALIAS "idf::esp_local_ctrl")
set(__component____idf_esp_local_ctrl_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_local_ctrl___PREFIX "idf")
set(__component____idf_esp_local_ctrl_KCONFIG "")
set(__component____idf_esp_local_ctrl_KCONFIG_PROJBUILD "")
set(__component____idf_esp_local_ctrl_SDKCONFIG_RENAME "")
set(__component____idf_esp_mm_COMPONENT_LIB "__idf_esp_mm")
set(__component____idf_esp_mm___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_mm_COMPONENT_NAME "esp_mm")
set(__component____idf_esp_mm_COMPONENT_DIR "C:/esp/esp-idf/components/esp_mm")
set(__component____idf_esp_mm_COMPONENT_ALIAS "idf::esp_mm")
set(__component____idf_esp_mm_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_mm___PREFIX "idf")
set(__component____idf_esp_mm_KCONFIG "C:/esp/esp-idf/components/esp_mm/Kconfig")
set(__component____idf_esp_mm_KCONFIG_PROJBUILD "")
set(__component____idf_esp_mm_SDKCONFIG_RENAME "")
set(__component____idf_esp_netif_COMPONENT_LIB "__idf_esp_netif")
set(__component____idf_esp_netif___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_netif_COMPONENT_NAME "esp_netif")
set(__component____idf_esp_netif_COMPONENT_DIR "C:/esp/esp-idf/components/esp_netif")
set(__component____idf_esp_netif_COMPONENT_ALIAS "idf::esp_netif")
set(__component____idf_esp_netif_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_netif___PREFIX "idf")
set(__component____idf_esp_netif_KCONFIG "C:/esp/esp-idf/components/esp_netif/Kconfig")
set(__component____idf_esp_netif_KCONFIG_PROJBUILD "")
set(__component____idf_esp_netif_SDKCONFIG_RENAME "")
set(__component____idf_esp_netif_stack_COMPONENT_LIB "__idf_esp_netif_stack")
set(__component____idf_esp_netif_stack___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_netif_stack_COMPONENT_NAME "esp_netif_stack")
set(__component____idf_esp_netif_stack_COMPONENT_DIR "C:/esp/esp-idf/components/esp_netif_stack")
set(__component____idf_esp_netif_stack_COMPONENT_ALIAS "idf::esp_netif_stack")
set(__component____idf_esp_netif_stack_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_netif_stack___PREFIX "idf")
set(__component____idf_esp_netif_stack_KCONFIG "")
set(__component____idf_esp_netif_stack_KCONFIG_PROJBUILD "")
set(__component____idf_esp_netif_stack_SDKCONFIG_RENAME "")
set(__component____idf_esp_partition_COMPONENT_LIB "__idf_esp_partition")
set(__component____idf_esp_partition___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_partition_COMPONENT_NAME "esp_partition")
set(__component____idf_esp_partition_COMPONENT_DIR "C:/esp/esp-idf/components/esp_partition")
set(__component____idf_esp_partition_COMPONENT_ALIAS "idf::esp_partition")
set(__component____idf_esp_partition_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_partition___PREFIX "idf")
set(__component____idf_esp_partition_KCONFIG "C:/esp/esp-idf/components/esp_partition/Kconfig")
set(__component____idf_esp_partition_KCONFIG_PROJBUILD "")
set(__component____idf_esp_partition_SDKCONFIG_RENAME "")
set(__component____idf_esp_phy_COMPONENT_LIB "__idf_esp_phy")
set(__component____idf_esp_phy___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_phy_COMPONENT_NAME "esp_phy")
set(__component____idf_esp_phy_COMPONENT_DIR "C:/esp/esp-idf/components/esp_phy")
set(__component____idf_esp_phy_COMPONENT_ALIAS "idf::esp_phy")
set(__component____idf_esp_phy_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_phy___PREFIX "idf")
set(__component____idf_esp_phy_KCONFIG "C:/esp/esp-idf/components/esp_phy/Kconfig")
set(__component____idf_esp_phy_KCONFIG_PROJBUILD "")
set(__component____idf_esp_phy_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_phy/sdkconfig.rename")
set(__component____idf_esp_pm_COMPONENT_LIB "__idf_esp_pm")
set(__component____idf_esp_pm___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_pm_COMPONENT_NAME "esp_pm")
set(__component____idf_esp_pm_COMPONENT_DIR "C:/esp/esp-idf/components/esp_pm")
set(__component____idf_esp_pm_COMPONENT_ALIAS "idf::esp_pm")
set(__component____idf_esp_pm_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_pm___PREFIX "idf")
set(__component____idf_esp_pm_KCONFIG "C:/esp/esp-idf/components/esp_pm/Kconfig")
set(__component____idf_esp_pm_KCONFIG_PROJBUILD "")
set(__component____idf_esp_pm_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_pm/sdkconfig.rename")
set(__component____idf_esp_psram_COMPONENT_LIB "__idf_esp_psram")
set(__component____idf_esp_psram___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_psram_COMPONENT_NAME "esp_psram")
set(__component____idf_esp_psram_COMPONENT_DIR "C:/esp/esp-idf/components/esp_psram")
set(__component____idf_esp_psram_COMPONENT_ALIAS "idf::esp_psram")
set(__component____idf_esp_psram_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_psram___PREFIX "idf")
set(__component____idf_esp_psram_KCONFIG "C:/esp/esp-idf/components/esp_psram/Kconfig")
set(__component____idf_esp_psram_KCONFIG_PROJBUILD "")
set(__component____idf_esp_psram_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_psram/sdkconfig.rename.esp32s3")
set(__component____idf_esp_ringbuf_COMPONENT_LIB "__idf_esp_ringbuf")
set(__component____idf_esp_ringbuf___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_ringbuf_COMPONENT_NAME "esp_ringbuf")
set(__component____idf_esp_ringbuf_COMPONENT_DIR "C:/esp/esp-idf/components/esp_ringbuf")
set(__component____idf_esp_ringbuf_COMPONENT_ALIAS "idf::esp_ringbuf")
set(__component____idf_esp_ringbuf_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_ringbuf___PREFIX "idf")
set(__component____idf_esp_ringbuf_KCONFIG "C:/esp/esp-idf/components/esp_ringbuf/Kconfig")
set(__component____idf_esp_ringbuf_KCONFIG_PROJBUILD "")
set(__component____idf_esp_ringbuf_SDKCONFIG_RENAME "")
set(__component____idf_esp_rom_COMPONENT_LIB "__idf_esp_rom")
set(__component____idf_esp_rom___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_rom_COMPONENT_NAME "esp_rom")
set(__component____idf_esp_rom_COMPONENT_DIR "C:/esp/esp-idf/components/esp_rom")
set(__component____idf_esp_rom_COMPONENT_ALIAS "idf::esp_rom")
set(__component____idf_esp_rom_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_rom___PREFIX "idf")
set(__component____idf_esp_rom_KCONFIG "C:/esp/esp-idf/components/esp_rom/Kconfig")
set(__component____idf_esp_rom_KCONFIG_PROJBUILD "C:/esp/esp-idf/components/esp_rom/Kconfig.projbuild")
set(__component____idf_esp_rom_SDKCONFIG_RENAME "")
set(__component____idf_esp_security_COMPONENT_LIB "__idf_esp_security")
set(__component____idf_esp_security___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_security_COMPONENT_NAME "esp_security")
set(__component____idf_esp_security_COMPONENT_DIR "C:/esp/esp-idf/components/esp_security")
set(__component____idf_esp_security_COMPONENT_ALIAS "idf::esp_security")
set(__component____idf_esp_security_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_security___PREFIX "idf")
set(__component____idf_esp_security_KCONFIG "C:/esp/esp-idf/components/esp_security/Kconfig")
set(__component____idf_esp_security_KCONFIG_PROJBUILD "")
set(__component____idf_esp_security_SDKCONFIG_RENAME "")
set(__component____idf_esp_system_COMPONENT_LIB "__idf_esp_system")
set(__component____idf_esp_system___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_system_COMPONENT_NAME "esp_system")
set(__component____idf_esp_system_COMPONENT_DIR "C:/esp/esp-idf/components/esp_system")
set(__component____idf_esp_system_COMPONENT_ALIAS "idf::esp_system")
set(__component____idf_esp_system_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_system___PREFIX "idf")
set(__component____idf_esp_system_KCONFIG "C:/esp/esp-idf/components/esp_system/Kconfig")
set(__component____idf_esp_system_KCONFIG_PROJBUILD "")
set(__component____idf_esp_system_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_system/sdkconfig.rename;C:/esp/esp-idf/components/esp_system/sdkconfig.rename.esp32s3")
set(__component____idf_esp_tee_COMPONENT_LIB "__idf_esp_tee")
set(__component____idf_esp_tee___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_tee_COMPONENT_NAME "esp_tee")
set(__component____idf_esp_tee_COMPONENT_DIR "C:/esp/esp-idf/components/esp_tee")
set(__component____idf_esp_tee_COMPONENT_ALIAS "idf::esp_tee")
set(__component____idf_esp_tee_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_tee___PREFIX "idf")
set(__component____idf_esp_tee_KCONFIG "")
set(__component____idf_esp_tee_KCONFIG_PROJBUILD "C:/esp/esp-idf/components/esp_tee/Kconfig.projbuild")
set(__component____idf_esp_tee_SDKCONFIG_RENAME "")
set(__component____idf_esp_timer_COMPONENT_LIB "__idf_esp_timer")
set(__component____idf_esp_timer___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_timer_COMPONENT_NAME "esp_timer")
set(__component____idf_esp_timer_COMPONENT_DIR "C:/esp/esp-idf/components/esp_timer")
set(__component____idf_esp_timer_COMPONENT_ALIAS "idf::esp_timer")
set(__component____idf_esp_timer_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_timer___PREFIX "idf")
set(__component____idf_esp_timer_KCONFIG "C:/esp/esp-idf/components/esp_timer/Kconfig")
set(__component____idf_esp_timer_KCONFIG_PROJBUILD "")
set(__component____idf_esp_timer_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_timer/sdkconfig.rename")
set(__component____idf_esp_vfs_console_COMPONENT_LIB "__idf_esp_vfs_console")
set(__component____idf_esp_vfs_console___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_vfs_console_COMPONENT_NAME "esp_vfs_console")
set(__component____idf_esp_vfs_console_COMPONENT_DIR "C:/esp/esp-idf/components/esp_vfs_console")
set(__component____idf_esp_vfs_console_COMPONENT_ALIAS "idf::esp_vfs_console")
set(__component____idf_esp_vfs_console_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_vfs_console___PREFIX "idf")
set(__component____idf_esp_vfs_console_KCONFIG "")
set(__component____idf_esp_vfs_console_KCONFIG_PROJBUILD "")
set(__component____idf_esp_vfs_console_SDKCONFIG_RENAME "")
set(__component____idf_esp_wifi_COMPONENT_LIB "__idf_esp_wifi")
set(__component____idf_esp_wifi___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp_wifi_COMPONENT_NAME "esp_wifi")
set(__component____idf_esp_wifi_COMPONENT_DIR "C:/esp/esp-idf/components/esp_wifi")
set(__component____idf_esp_wifi_COMPONENT_ALIAS "idf::esp_wifi")
set(__component____idf_esp_wifi_COMPONENT_SOURCE "idf_components")
set(__component____idf_esp_wifi___PREFIX "idf")
set(__component____idf_esp_wifi_KCONFIG "C:/esp/esp-idf/components/esp_wifi/Kconfig")
set(__component____idf_esp_wifi_KCONFIG_PROJBUILD "")
set(__component____idf_esp_wifi_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esp_wifi/sdkconfig.rename")
set(__component____idf_espcoredump_COMPONENT_LIB "__idf_espcoredump")
set(__component____idf_espcoredump___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_espcoredump_COMPONENT_NAME "espcoredump")
set(__component____idf_espcoredump_COMPONENT_DIR "C:/esp/esp-idf/components/espcoredump")
set(__component____idf_espcoredump_COMPONENT_ALIAS "idf::espcoredump")
set(__component____idf_espcoredump_COMPONENT_SOURCE "idf_components")
set(__component____idf_espcoredump___PREFIX "idf")
set(__component____idf_espcoredump_KCONFIG "C:/esp/esp-idf/components/espcoredump/Kconfig")
set(__component____idf_espcoredump_KCONFIG_PROJBUILD "")
set(__component____idf_espcoredump_SDKCONFIG_RENAME "C:/esp/esp-idf/components/espcoredump/sdkconfig.rename")
set(__component____idf_esptool_py_COMPONENT_LIB "__idf_esptool_py")
set(__component____idf_esptool_py___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esptool_py_COMPONENT_NAME "esptool_py")
set(__component____idf_esptool_py_COMPONENT_DIR "C:/esp/esp-idf/components/esptool_py")
set(__component____idf_esptool_py_COMPONENT_ALIAS "idf::esptool_py")
set(__component____idf_esptool_py_COMPONENT_SOURCE "idf_components")
set(__component____idf_esptool_py___PREFIX "idf")
set(__component____idf_esptool_py_KCONFIG "")
set(__component____idf_esptool_py_KCONFIG_PROJBUILD "C:/esp/esp-idf/components/esptool_py/Kconfig.projbuild")
set(__component____idf_esptool_py_SDKCONFIG_RENAME "C:/esp/esp-idf/components/esptool_py/sdkconfig.rename")
set(__component____idf_fatfs_COMPONENT_LIB "__idf_fatfs")
set(__component____idf_fatfs___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_fatfs_COMPONENT_NAME "fatfs")
set(__component____idf_fatfs_COMPONENT_DIR "C:/esp/esp-idf/components/fatfs")
set(__component____idf_fatfs_COMPONENT_ALIAS "idf::fatfs")
set(__component____idf_fatfs_COMPONENT_SOURCE "idf_components")
set(__component____idf_fatfs___PREFIX "idf")
set(__component____idf_fatfs_KCONFIG "C:/esp/esp-idf/components/fatfs/Kconfig")
set(__component____idf_fatfs_KCONFIG_PROJBUILD "")
set(__component____idf_fatfs_SDKCONFIG_RENAME "")
set(__component____idf_freertos_COMPONENT_LIB "__idf_freertos")
set(__component____idf_freertos___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_freertos_COMPONENT_NAME "freertos")
set(__component____idf_freertos_COMPONENT_DIR "C:/esp/esp-idf/components/freertos")
set(__component____idf_freertos_COMPONENT_ALIAS "idf::freertos")
set(__component____idf_freertos_COMPONENT_SOURCE "idf_components")
set(__component____idf_freertos___PREFIX "idf")
set(__component____idf_freertos_KCONFIG "C:/esp/esp-idf/components/freertos/Kconfig")
set(__component____idf_freertos_KCONFIG_PROJBUILD "")
set(__component____idf_freertos_SDKCONFIG_RENAME "C:/esp/esp-idf/components/freertos/sdkconfig.rename")
set(__component____idf_hal_COMPONENT_LIB "__idf_hal")
set(__component____idf_hal___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_hal_COMPONENT_NAME "hal")
set(__component____idf_hal_COMPONENT_DIR "C:/esp/esp-idf/components/hal")
set(__component____idf_hal_COMPONENT_ALIAS "idf::hal")
set(__component____idf_hal_COMPONENT_SOURCE "idf_components")
set(__component____idf_hal___PREFIX "idf")
set(__component____idf_hal_KCONFIG "C:/esp/esp-idf/components/hal/Kconfig")
set(__component____idf_hal_KCONFIG_PROJBUILD "")
set(__component____idf_hal_SDKCONFIG_RENAME "C:/esp/esp-idf/components/hal/sdkconfig.rename")
set(__component____idf_heap_COMPONENT_LIB "__idf_heap")
set(__component____idf_heap___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_heap_COMPONENT_NAME "heap")
set(__component____idf_heap_COMPONENT_DIR "C:/esp/esp-idf/components/heap")
set(__component____idf_heap_COMPONENT_ALIAS "idf::heap")
set(__component____idf_heap_COMPONENT_SOURCE "idf_components")
set(__component____idf_heap___PREFIX "idf")
set(__component____idf_heap_KCONFIG "C:/esp/esp-idf/components/heap/Kconfig")
set(__component____idf_heap_KCONFIG_PROJBUILD "")
set(__component____idf_heap_SDKCONFIG_RENAME "")
set(__component____idf_http_parser_COMPONENT_LIB "__idf_http_parser")
set(__component____idf_http_parser___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_http_parser_COMPONENT_NAME "http_parser")
set(__component____idf_http_parser_COMPONENT_DIR "C:/esp/esp-idf/components/http_parser")
set(__component____idf_http_parser_COMPONENT_ALIAS "idf::http_parser")
set(__component____idf_http_parser_COMPONENT_SOURCE "idf_components")
set(__component____idf_http_parser___PREFIX "idf")
set(__component____idf_http_parser_KCONFIG "")
set(__component____idf_http_parser_KCONFIG_PROJBUILD "")
set(__component____idf_http_parser_SDKCONFIG_RENAME "")
set(__component____idf_idf_test_COMPONENT_LIB "__idf_idf_test")
set(__component____idf_idf_test___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_idf_test_COMPONENT_NAME "idf_test")
set(__component____idf_idf_test_COMPONENT_DIR "C:/esp/esp-idf/components/idf_test")
set(__component____idf_idf_test_COMPONENT_ALIAS "idf::idf_test")
set(__component____idf_idf_test_COMPONENT_SOURCE "idf_components")
set(__component____idf_idf_test___PREFIX "idf")
set(__component____idf_idf_test_KCONFIG "")
set(__component____idf_idf_test_KCONFIG_PROJBUILD "")
set(__component____idf_idf_test_SDKCONFIG_RENAME "")
set(__component____idf_ieee802154_COMPONENT_LIB "__idf_ieee802154")
set(__component____idf_ieee802154___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_ieee802154_COMPONENT_NAME "ieee802154")
set(__component____idf_ieee802154_COMPONENT_DIR "C:/esp/esp-idf/components/ieee802154")
set(__component____idf_ieee802154_COMPONENT_ALIAS "idf::ieee802154")
set(__component____idf_ieee802154_COMPONENT_SOURCE "idf_components")
set(__component____idf_ieee802154___PREFIX "idf")
set(__component____idf_ieee802154_KCONFIG "C:/esp/esp-idf/components/ieee802154/Kconfig")
set(__component____idf_ieee802154_KCONFIG_PROJBUILD "")
set(__component____idf_ieee802154_SDKCONFIG_RENAME "")
set(__component____idf_json_COMPONENT_LIB "__idf_json")
set(__component____idf_json___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_json_COMPONENT_NAME "json")
set(__component____idf_json_COMPONENT_DIR "C:/esp/esp-idf/components/json")
set(__component____idf_json_COMPONENT_ALIAS "idf::json")
set(__component____idf_json_COMPONENT_SOURCE "idf_components")
set(__component____idf_json___PREFIX "idf")
set(__component____idf_json_KCONFIG "")
set(__component____idf_json_KCONFIG_PROJBUILD "")
set(__component____idf_json_SDKCONFIG_RENAME "")
set(__component____idf_linux_COMPONENT_LIB "__idf_linux")
set(__component____idf_linux___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_linux_COMPONENT_NAME "linux")
set(__component____idf_linux_COMPONENT_DIR "C:/esp/esp-idf/components/linux")
set(__component____idf_linux_COMPONENT_ALIAS "idf::linux")
set(__component____idf_linux_COMPONENT_SOURCE "idf_components")
set(__component____idf_linux___PREFIX "idf")
set(__component____idf_linux_KCONFIG "")
set(__component____idf_linux_KCONFIG_PROJBUILD "")
set(__component____idf_linux_SDKCONFIG_RENAME "")
set(__component____idf_log_COMPONENT_LIB "__idf_log")
set(__component____idf_log___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_log_COMPONENT_NAME "log")
set(__component____idf_log_COMPONENT_DIR "C:/esp/esp-idf/components/log")
set(__component____idf_log_COMPONENT_ALIAS "idf::log")
set(__component____idf_log_COMPONENT_SOURCE "idf_components")
set(__component____idf_log___PREFIX "idf")
set(__component____idf_log_KCONFIG "C:/esp/esp-idf/components/log/Kconfig")
set(__component____idf_log_KCONFIG_PROJBUILD "")
set(__component____idf_log_SDKCONFIG_RENAME "")
set(__component____idf_lwip_COMPONENT_LIB "__idf_lwip")
set(__component____idf_lwip___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_lwip_COMPONENT_NAME "lwip")
set(__component____idf_lwip_COMPONENT_DIR "C:/esp/esp-idf/components/lwip")
set(__component____idf_lwip_COMPONENT_ALIAS "idf::lwip")
set(__component____idf_lwip_COMPONENT_SOURCE "idf_components")
set(__component____idf_lwip___PREFIX "idf")
set(__component____idf_lwip_KCONFIG "C:/esp/esp-idf/components/lwip/Kconfig")
set(__component____idf_lwip_KCONFIG_PROJBUILD "")
set(__component____idf_lwip_SDKCONFIG_RENAME "C:/esp/esp-idf/components/lwip/sdkconfig.rename")
set(__component____idf_mbedtls_COMPONENT_LIB "__idf_mbedtls")
set(__component____idf_mbedtls___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_mbedtls_COMPONENT_NAME "mbedtls")
set(__component____idf_mbedtls_COMPONENT_DIR "C:/esp/esp-idf/components/mbedtls")
set(__component____idf_mbedtls_COMPONENT_ALIAS "idf::mbedtls")
set(__component____idf_mbedtls_COMPONENT_SOURCE "idf_components")
set(__component____idf_mbedtls___PREFIX "idf")
set(__component____idf_mbedtls_KCONFIG "C:/esp/esp-idf/components/mbedtls/Kconfig")
set(__component____idf_mbedtls_KCONFIG_PROJBUILD "")
set(__component____idf_mbedtls_SDKCONFIG_RENAME "")
set(__component____idf_mqtt_COMPONENT_LIB "__idf_mqtt")
set(__component____idf_mqtt___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_mqtt_COMPONENT_NAME "mqtt")
set(__component____idf_mqtt_COMPONENT_DIR "C:/esp/esp-idf/components/mqtt")
set(__component____idf_mqtt_COMPONENT_ALIAS "idf::mqtt")
set(__component____idf_mqtt_COMPONENT_SOURCE "idf_components")
set(__component____idf_mqtt___PREFIX "idf")
set(__component____idf_mqtt_KCONFIG "")
set(__component____idf_mqtt_KCONFIG_PROJBUILD "")
set(__component____idf_mqtt_SDKCONFIG_RENAME "")
set(__component____idf_newlib_COMPONENT_LIB "__idf_newlib")
set(__component____idf_newlib___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_newlib_COMPONENT_NAME "newlib")
set(__component____idf_newlib_COMPONENT_DIR "C:/esp/esp-idf/components/newlib")
set(__component____idf_newlib_COMPONENT_ALIAS "idf::newlib")
set(__component____idf_newlib_COMPONENT_SOURCE "idf_components")
set(__component____idf_newlib___PREFIX "idf")
set(__component____idf_newlib_KCONFIG "C:/esp/esp-idf/components/newlib/Kconfig")
set(__component____idf_newlib_KCONFIG_PROJBUILD "")
set(__component____idf_newlib_SDKCONFIG_RENAME "C:/esp/esp-idf/components/newlib/sdkconfig.rename;C:/esp/esp-idf/components/newlib/sdkconfig.rename.esp32s3")
set(__component____idf_nvs_flash_COMPONENT_LIB "__idf_nvs_flash")
set(__component____idf_nvs_flash___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_nvs_flash_COMPONENT_NAME "nvs_flash")
set(__component____idf_nvs_flash_COMPONENT_DIR "C:/esp/esp-idf/components/nvs_flash")
set(__component____idf_nvs_flash_COMPONENT_ALIAS "idf::nvs_flash")
set(__component____idf_nvs_flash_COMPONENT_SOURCE "idf_components")
set(__component____idf_nvs_flash___PREFIX "idf")
set(__component____idf_nvs_flash_KCONFIG "C:/esp/esp-idf/components/nvs_flash/Kconfig")
set(__component____idf_nvs_flash_KCONFIG_PROJBUILD "")
set(__component____idf_nvs_flash_SDKCONFIG_RENAME "")
set(__component____idf_nvs_sec_provider_COMPONENT_LIB "__idf_nvs_sec_provider")
set(__component____idf_nvs_sec_provider___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_nvs_sec_provider_COMPONENT_NAME "nvs_sec_provider")
set(__component____idf_nvs_sec_provider_COMPONENT_DIR "C:/esp/esp-idf/components/nvs_sec_provider")
set(__component____idf_nvs_sec_provider_COMPONENT_ALIAS "idf::nvs_sec_provider")
set(__component____idf_nvs_sec_provider_COMPONENT_SOURCE "idf_components")
set(__component____idf_nvs_sec_provider___PREFIX "idf")
set(__component____idf_nvs_sec_provider_KCONFIG "C:/esp/esp-idf/components/nvs_sec_provider/Kconfig")
set(__component____idf_nvs_sec_provider_KCONFIG_PROJBUILD "")
set(__component____idf_nvs_sec_provider_SDKCONFIG_RENAME "")
set(__component____idf_openthread_COMPONENT_LIB "__idf_openthread")
set(__component____idf_openthread___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_openthread_COMPONENT_NAME "openthread")
set(__component____idf_openthread_COMPONENT_DIR "C:/esp/esp-idf/components/openthread")
set(__component____idf_openthread_COMPONENT_ALIAS "idf::openthread")
set(__component____idf_openthread_COMPONENT_SOURCE "idf_components")
set(__component____idf_openthread___PREFIX "idf")
set(__component____idf_openthread_KCONFIG "C:/esp/esp-idf/components/openthread/Kconfig")
set(__component____idf_openthread_KCONFIG_PROJBUILD "")
set(__component____idf_openthread_SDKCONFIG_RENAME "")
set(__component____idf_partition_table_COMPONENT_LIB "__idf_partition_table")
set(__component____idf_partition_table___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_partition_table_COMPONENT_NAME "partition_table")
set(__component____idf_partition_table_COMPONENT_DIR "C:/esp/esp-idf/components/partition_table")
set(__component____idf_partition_table_COMPONENT_ALIAS "idf::partition_table")
set(__component____idf_partition_table_COMPONENT_SOURCE "idf_components")
set(__component____idf_partition_table___PREFIX "idf")
set(__component____idf_partition_table_KCONFIG "")
set(__component____idf_partition_table_KCONFIG_PROJBUILD "C:/esp/esp-idf/components/partition_table/Kconfig.projbuild")
set(__component____idf_partition_table_SDKCONFIG_RENAME "")
set(__component____idf_perfmon_COMPONENT_LIB "__idf_perfmon")
set(__component____idf_perfmon___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_perfmon_COMPONENT_NAME "perfmon")
set(__component____idf_perfmon_COMPONENT_DIR "C:/esp/esp-idf/components/perfmon")
set(__component____idf_perfmon_COMPONENT_ALIAS "idf::perfmon")
set(__component____idf_perfmon_COMPONENT_SOURCE "idf_components")
set(__component____idf_perfmon___PREFIX "idf")
set(__component____idf_perfmon_KCONFIG "")
set(__component____idf_perfmon_KCONFIG_PROJBUILD "")
set(__component____idf_perfmon_SDKCONFIG_RENAME "")
set(__component____idf_protobuf-c_COMPONENT_LIB "__idf_protobuf-c")
set(__component____idf_protobuf-c___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_protobuf-c_COMPONENT_NAME "protobuf-c")
set(__component____idf_protobuf-c_COMPONENT_DIR "C:/esp/esp-idf/components/protobuf-c")
set(__component____idf_protobuf-c_COMPONENT_ALIAS "idf::protobuf-c")
set(__component____idf_protobuf-c_COMPONENT_SOURCE "idf_components")
set(__component____idf_protobuf-c___PREFIX "idf")
set(__component____idf_protobuf-c_KCONFIG "")
set(__component____idf_protobuf-c_KCONFIG_PROJBUILD "")
set(__component____idf_protobuf-c_SDKCONFIG_RENAME "")
set(__component____idf_protocomm_COMPONENT_LIB "__idf_protocomm")
set(__component____idf_protocomm___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_protocomm_COMPONENT_NAME "protocomm")
set(__component____idf_protocomm_COMPONENT_DIR "C:/esp/esp-idf/components/protocomm")
set(__component____idf_protocomm_COMPONENT_ALIAS "idf::protocomm")
set(__component____idf_protocomm_COMPONENT_SOURCE "idf_components")
set(__component____idf_protocomm___PREFIX "idf")
set(__component____idf_protocomm_KCONFIG "C:/esp/esp-idf/components/protocomm/Kconfig")
set(__component____idf_protocomm_KCONFIG_PROJBUILD "")
set(__component____idf_protocomm_SDKCONFIG_RENAME "")
set(__component____idf_pthread_COMPONENT_LIB "__idf_pthread")
set(__component____idf_pthread___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_pthread_COMPONENT_NAME "pthread")
set(__component____idf_pthread_COMPONENT_DIR "C:/esp/esp-idf/components/pthread")
set(__component____idf_pthread_COMPONENT_ALIAS "idf::pthread")
set(__component____idf_pthread_COMPONENT_SOURCE "idf_components")
set(__component____idf_pthread___PREFIX "idf")
set(__component____idf_pthread_KCONFIG "C:/esp/esp-idf/components/pthread/Kconfig")
set(__component____idf_pthread_KCONFIG_PROJBUILD "")
set(__component____idf_pthread_SDKCONFIG_RENAME "C:/esp/esp-idf/components/pthread/sdkconfig.rename")
set(__component____idf_riscv_COMPONENT_LIB "__idf_riscv")
set(__component____idf_riscv___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_riscv_COMPONENT_NAME "riscv")
set(__component____idf_riscv_COMPONENT_DIR "C:/esp/esp-idf/components/riscv")
set(__component____idf_riscv_COMPONENT_ALIAS "idf::riscv")
set(__component____idf_riscv_COMPONENT_SOURCE "idf_components")
set(__component____idf_riscv___PREFIX "idf")
set(__component____idf_riscv_KCONFIG "")
set(__component____idf_riscv_KCONFIG_PROJBUILD "")
set(__component____idf_riscv_SDKCONFIG_RENAME "")
set(__component____idf_rt_COMPONENT_LIB "__idf_rt")
set(__component____idf_rt___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_rt_COMPONENT_NAME "rt")
set(__component____idf_rt_COMPONENT_DIR "C:/esp/esp-idf/components/rt")
set(__component____idf_rt_COMPONENT_ALIAS "idf::rt")
set(__component____idf_rt_COMPONENT_SOURCE "idf_components")
set(__component____idf_rt___PREFIX "idf")
set(__component____idf_rt_KCONFIG "")
set(__component____idf_rt_KCONFIG_PROJBUILD "")
set(__component____idf_rt_SDKCONFIG_RENAME "")
set(__component____idf_sdmmc_COMPONENT_LIB "__idf_sdmmc")
set(__component____idf_sdmmc___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_sdmmc_COMPONENT_NAME "sdmmc")
set(__component____idf_sdmmc_COMPONENT_DIR "C:/esp/esp-idf/components/sdmmc")
set(__component____idf_sdmmc_COMPONENT_ALIAS "idf::sdmmc")
set(__component____idf_sdmmc_COMPONENT_SOURCE "idf_components")
set(__component____idf_sdmmc___PREFIX "idf")
set(__component____idf_sdmmc_KCONFIG "")
set(__component____idf_sdmmc_KCONFIG_PROJBUILD "")
set(__component____idf_sdmmc_SDKCONFIG_RENAME "")
set(__component____idf_soc_COMPONENT_LIB "__idf_soc")
set(__component____idf_soc___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_soc_COMPONENT_NAME "soc")
set(__component____idf_soc_COMPONENT_DIR "C:/esp/esp-idf/components/soc")
set(__component____idf_soc_COMPONENT_ALIAS "idf::soc")
set(__component____idf_soc_COMPONENT_SOURCE "idf_components")
set(__component____idf_soc___PREFIX "idf")
set(__component____idf_soc_KCONFIG "C:/esp/esp-idf/components/soc/Kconfig")
set(__component____idf_soc_KCONFIG_PROJBUILD "")
set(__component____idf_soc_SDKCONFIG_RENAME "")
set(__component____idf_spi_flash_COMPONENT_LIB "__idf_spi_flash")
set(__component____idf_spi_flash___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_spi_flash_COMPONENT_NAME "spi_flash")
set(__component____idf_spi_flash_COMPONENT_DIR "C:/esp/esp-idf/components/spi_flash")
set(__component____idf_spi_flash_COMPONENT_ALIAS "idf::spi_flash")
set(__component____idf_spi_flash_COMPONENT_SOURCE "idf_components")
set(__component____idf_spi_flash___PREFIX "idf")
set(__component____idf_spi_flash_KCONFIG "C:/esp/esp-idf/components/spi_flash/Kconfig")
set(__component____idf_spi_flash_KCONFIG_PROJBUILD "")
set(__component____idf_spi_flash_SDKCONFIG_RENAME "C:/esp/esp-idf/components/spi_flash/sdkconfig.rename")
set(__component____idf_spiffs_COMPONENT_LIB "__idf_spiffs")
set(__component____idf_spiffs___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_spiffs_COMPONENT_NAME "spiffs")
set(__component____idf_spiffs_COMPONENT_DIR "C:/esp/esp-idf/components/spiffs")
set(__component____idf_spiffs_COMPONENT_ALIAS "idf::spiffs")
set(__component____idf_spiffs_COMPONENT_SOURCE "idf_components")
set(__component____idf_spiffs___PREFIX "idf")
set(__component____idf_spiffs_KCONFIG "C:/esp/esp-idf/components/spiffs/Kconfig")
set(__component____idf_spiffs_KCONFIG_PROJBUILD "")
set(__component____idf_spiffs_SDKCONFIG_RENAME "")
set(__component____idf_tcp_transport_COMPONENT_LIB "__idf_tcp_transport")
set(__component____idf_tcp_transport___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_tcp_transport_COMPONENT_NAME "tcp_transport")
set(__component____idf_tcp_transport_COMPONENT_DIR "C:/esp/esp-idf/components/tcp_transport")
set(__component____idf_tcp_transport_COMPONENT_ALIAS "idf::tcp_transport")
set(__component____idf_tcp_transport_COMPONENT_SOURCE "idf_components")
set(__component____idf_tcp_transport___PREFIX "idf")
set(__component____idf_tcp_transport_KCONFIG "C:/esp/esp-idf/components/tcp_transport/Kconfig")
set(__component____idf_tcp_transport_KCONFIG_PROJBUILD "")
set(__component____idf_tcp_transport_SDKCONFIG_RENAME "")
set(__component____idf_touch_element_COMPONENT_LIB "__idf_touch_element")
set(__component____idf_touch_element___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_touch_element_COMPONENT_NAME "touch_element")
set(__component____idf_touch_element_COMPONENT_DIR "C:/esp/esp-idf/components/touch_element")
set(__component____idf_touch_element_COMPONENT_ALIAS "idf::touch_element")
set(__component____idf_touch_element_COMPONENT_SOURCE "idf_components")
set(__component____idf_touch_element___PREFIX "idf")
set(__component____idf_touch_element_KCONFIG "")
set(__component____idf_touch_element_KCONFIG_PROJBUILD "")
set(__component____idf_touch_element_SDKCONFIG_RENAME "")
set(__component____idf_ulp_COMPONENT_LIB "__idf_ulp")
set(__component____idf_ulp___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_ulp_COMPONENT_NAME "ulp")
set(__component____idf_ulp_COMPONENT_DIR "C:/esp/esp-idf/components/ulp")
set(__component____idf_ulp_COMPONENT_ALIAS "idf::ulp")
set(__component____idf_ulp_COMPONENT_SOURCE "idf_components")
set(__component____idf_ulp___PREFIX "idf")
set(__component____idf_ulp_KCONFIG "C:/esp/esp-idf/components/ulp/Kconfig")
set(__component____idf_ulp_KCONFIG_PROJBUILD "")
set(__component____idf_ulp_SDKCONFIG_RENAME "")
set(__component____idf_unity_COMPONENT_LIB "__idf_unity")
set(__component____idf_unity___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_unity_COMPONENT_NAME "unity")
set(__component____idf_unity_COMPONENT_DIR "C:/esp/esp-idf/components/unity")
set(__component____idf_unity_COMPONENT_ALIAS "idf::unity")
set(__component____idf_unity_COMPONENT_SOURCE "idf_components")
set(__component____idf_unity___PREFIX "idf")
set(__component____idf_unity_KCONFIG "C:/esp/esp-idf/components/unity/Kconfig")
set(__component____idf_unity_KCONFIG_PROJBUILD "")
set(__component____idf_unity_SDKCONFIG_RENAME "")
set(__component____idf_usb_COMPONENT_LIB "__idf_usb")
set(__component____idf_usb___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_usb_COMPONENT_NAME "usb")
set(__component____idf_usb_COMPONENT_DIR "C:/esp/esp-idf/components/usb")
set(__component____idf_usb_COMPONENT_ALIAS "idf::usb")
set(__component____idf_usb_COMPONENT_SOURCE "idf_components")
set(__component____idf_usb___PREFIX "idf")
set(__component____idf_usb_KCONFIG "C:/esp/esp-idf/components/usb/Kconfig")
set(__component____idf_usb_KCONFIG_PROJBUILD "")
set(__component____idf_usb_SDKCONFIG_RENAME "")
set(__component____idf_vfs_COMPONENT_LIB "__idf_vfs")
set(__component____idf_vfs___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_vfs_COMPONENT_NAME "vfs")
set(__component____idf_vfs_COMPONENT_DIR "C:/esp/esp-idf/components/vfs")
set(__component____idf_vfs_COMPONENT_ALIAS "idf::vfs")
set(__component____idf_vfs_COMPONENT_SOURCE "idf_components")
set(__component____idf_vfs___PREFIX "idf")
set(__component____idf_vfs_KCONFIG "C:/esp/esp-idf/components/vfs/Kconfig")
set(__component____idf_vfs_KCONFIG_PROJBUILD "")
set(__component____idf_vfs_SDKCONFIG_RENAME "C:/esp/esp-idf/components/vfs/sdkconfig.rename")
set(__component____idf_wear_levelling_COMPONENT_LIB "__idf_wear_levelling")
set(__component____idf_wear_levelling___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_wear_levelling_COMPONENT_NAME "wear_levelling")
set(__component____idf_wear_levelling_COMPONENT_DIR "C:/esp/esp-idf/components/wear_levelling")
set(__component____idf_wear_levelling_COMPONENT_ALIAS "idf::wear_levelling")
set(__component____idf_wear_levelling_COMPONENT_SOURCE "idf_components")
set(__component____idf_wear_levelling___PREFIX "idf")
set(__component____idf_wear_levelling_KCONFIG "C:/esp/esp-idf/components/wear_levelling/Kconfig")
set(__component____idf_wear_levelling_KCONFIG_PROJBUILD "")
set(__component____idf_wear_levelling_SDKCONFIG_RENAME "")
set(__component____idf_wifi_provisioning_COMPONENT_LIB "__idf_wifi_provisioning")
set(__component____idf_wifi_provisioning___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_wifi_provisioning_COMPONENT_NAME "wifi_provisioning")
set(__component____idf_wifi_provisioning_COMPONENT_DIR "C:/esp/esp-idf/components/wifi_provisioning")
set(__component____idf_wifi_provisioning_COMPONENT_ALIAS "idf::wifi_provisioning")
set(__component____idf_wifi_provisioning_COMPONENT_SOURCE "idf_components")
set(__component____idf_wifi_provisioning___PREFIX "idf")
set(__component____idf_wifi_provisioning_KCONFIG "C:/esp/esp-idf/components/wifi_provisioning/Kconfig")
set(__component____idf_wifi_provisioning_KCONFIG_PROJBUILD "")
set(__component____idf_wifi_provisioning_SDKCONFIG_RENAME "")
set(__component____idf_wpa_supplicant_COMPONENT_LIB "__idf_wpa_supplicant")
set(__component____idf_wpa_supplicant___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_wpa_supplicant_COMPONENT_NAME "wpa_supplicant")
set(__component____idf_wpa_supplicant_COMPONENT_DIR "C:/esp/esp-idf/components/wpa_supplicant")
set(__component____idf_wpa_supplicant_COMPONENT_ALIAS "idf::wpa_supplicant")
set(__component____idf_wpa_supplicant_COMPONENT_SOURCE "idf_components")
set(__component____idf_wpa_supplicant___PREFIX "idf")
set(__component____idf_wpa_supplicant_KCONFIG "")
set(__component____idf_wpa_supplicant_KCONFIG_PROJBUILD "")
set(__component____idf_wpa_supplicant_SDKCONFIG_RENAME "")
set(__component____idf_xtensa_COMPONENT_LIB "__idf_xtensa")
set(__component____idf_xtensa___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_xtensa_COMPONENT_NAME "xtensa")
set(__component____idf_xtensa_COMPONENT_DIR "C:/esp/esp-idf/components/xtensa")
set(__component____idf_xtensa_COMPONENT_ALIAS "idf::xtensa")
set(__component____idf_xtensa_COMPONENT_SOURCE "idf_components")
set(__component____idf_xtensa___PREFIX "idf")
set(__component____idf_xtensa_KCONFIG "")
set(__component____idf_xtensa_KCONFIG_PROJBUILD "")
set(__component____idf_xtensa_SDKCONFIG_RENAME "")
set(__component____idf_main_COMPONENT_LIB "__idf_main")
set(__component____idf_main___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_main_COMPONENT_NAME "main")
set(__component____idf_main_COMPONENT_DIR "D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/main")
set(__component____idf_main_COMPONENT_ALIAS "idf::main")
set(__component____idf_main_COMPONENT_SOURCE "project_components")
set(__component____idf_main___PREFIX "idf")
set(__component____idf_main_KCONFIG "")
set(__component____idf_main_KCONFIG_PROJBUILD "")
set(__component____idf_main_SDKCONFIG_RENAME "")
set(__component____idf_esp32-camera_COMPONENT_LIB "__idf_esp32-camera")
set(__component____idf_esp32-camera___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_esp32-camera_COMPONENT_NAME "esp32-camera")
set(__component____idf_esp32-camera_COMPONENT_DIR "D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/components/esp32-camera")
set(__component____idf_esp32-camera_COMPONENT_ALIAS "idf::esp32-camera")
set(__component____idf_esp32-camera_COMPONENT_SOURCE "project_components")
set(__component____idf_esp32-camera___PREFIX "idf")
set(__component____idf_esp32-camera_KCONFIG "")
set(__component____idf_esp32-camera_KCONFIG_PROJBUILD "")
set(__component____idf_esp32-camera_SDKCONFIG_RENAME "")
set(__component____idf_tflite-micro_COMPONENT_LIB "__idf_tflite-micro")
set(__component____idf_tflite-micro___COMPONENT_PROPERTIES "COMPONENT_LIB;__COMPONENT_PROPERTIES;COMPONENT_NAME;COMPONENT_DIR;COMPONENT_ALIAS;COMPONENT_SOURCE;__PREFIX;KCONFIG;KCONFIG_PROJBUILD;SDKCONFIG_RENAME")
set(__component____idf_tflite-micro_COMPONENT_NAME "tflite-micro")
set(__component____idf_tflite-micro_COMPONENT_DIR "D:/OBJECT_DETECTION/ESP_PROJECTS/DETECTRA_ESP32S3/components/tflite-micro")
set(__component____idf_tflite-micro_COMPONENT_ALIAS "idf::tflite-micro")
set(__component____idf_tflite-micro_COMPONENT_SOURCE "project_components")
set(__component____idf_tflite-micro___PREFIX "idf")
set(__component____idf_tflite-micro_KCONFIG "")
set(__component____idf_tflite-micro_KCONFIG_PROJBUILD "")
set(__component____idf_tflite-micro_SDKCONFIG_RENAME "")