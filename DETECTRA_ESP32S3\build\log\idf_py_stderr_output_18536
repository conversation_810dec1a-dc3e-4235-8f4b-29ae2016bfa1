Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=C:\Users\<USER>\.espressif\python_env\idf6.0_py3.9_env\Scripts\python.exe -DESP_PLATFORM=1 -DIDF_TARGET=esp32s3 -DCCACHE_ENABLE=1 D:\OBJECT_DETECTION\ESP_PROJECTS\DETECTRA_ESP32S3
fatal: not a git repository: C:/esp/esp-idf/.git/modules/components/esp_wifi/lib
CMake Warning at C:/esp/esp-idf/tools/cmake/git_submodules.cmake:52 (message):
  Git submodule
  components/bootloader/subproject/components/micro-ecc/micro-ecc is out of
  date.  Run the following command to fix: git submodule update --init
  --recursive
Call Stack (most recent call first):
  C:/esp/esp-idf/tools/cmake/build.cmake:109 (git_submodule_check)
  C:/esp/esp-idf/tools/cmake/build.cmake:282 (__build_get_idf_git_revision)
  C:/esp/esp-idf/tools/cmake/idf.cmake:55 (__build_init)
  C:/esp/esp-idf/tools/cmake/project.cmake:29 (include)
  CMakeLists.txt:9 (include)


CMake Warning at C:/esp/esp-idf/tools/cmake/git_submodules.cmake:52 (message):
  Git submodule components/bt/controller/lib_esp32 is out of date.  Run the
  following command to fix: git submodule update --init --recursive
Call Stack (most recent call first):
  C:/esp/esp-idf/tools/cmake/build.cmake:109 (git_submodule_check)
  C:/esp/esp-idf/tools/cmake/build.cmake:282 (__build_get_idf_git_revision)
  C:/esp/esp-idf/tools/cmake/idf.cmake:55 (__build_init)
  C:/esp/esp-idf/tools/cmake/project.cmake:29 (include)
  CMakeLists.txt:9 (include)


CMake Warning at C:/esp/esp-idf/tools/cmake/git_submodules.cmake:52 (message):
  Git submodule components/cmock/CMock is out of date.  Run the following
  command to fix: git submodule update --init --recursive
Call Stack (most recent call first):
  C:/esp/esp-idf/tools/cmake/build.cmake:109 (git_submodule_check)
  C:/esp/esp-idf/tools/cmake/build.cmake:282 (__build_get_idf_git_revision)
  C:/esp/esp-idf/tools/cmake/idf.cmake:55 (__build_init)
  C:/esp/esp-idf/tools/cmake/project.cmake:29 (include)
  CMakeLists.txt:9 (include)


CMake Error at C:/esp/esp-idf/tools/cmake/component.cmake:229 (message):
  CMake Warning (dev) at build_properties.temp.cmake:8:

    Syntax Warning in cmake code at column 106

  

    Argument not separated from preceding token by whitespace.

  Call Stack (most recent call first):

    C:/esp/esp-idf/tools/cmake/scripts/component_get_requirements.cmake:3 (include)

  This warning is for project developers.  Use -Wno-dev to suppress it.



  CMake Error at C:/esp/esp-idf/components/mqtt/CMakeLists.txt:3 (message):

    Missing esp-mqtt submodule.  Please run 'git submodule update --init
    --recursive' in ESP-IDF directory to fix this.

  Call Stack (most recent call first):

    C:/esp/esp-idf/tools/cmake/scripts/component_get_requirements.cmake:107 (include)
    C:/esp/esp-idf/tools/cmake/scripts/component_get_requirements.cmake:160 (__component_get_requirements)

  



Call Stack (most recent call first):
  C:/esp/esp-idf/tools/cmake/build.cmake:664 (__component_get_requirements)
  C:/esp/esp-idf/tools/cmake/project.cmake:740 (idf_build_process)
  CMakeLists.txt:12 (project)


