# ESP32 Camera component

# This component will use the official ESP32 Camera library
# You can either:
# 1. Add it as a git submodule
# 2. Use ESP Component Registry
# 3. Download and include manually

# For ESP Component Registry approach, add this to your main CMakeLists.txt:
# idf_component_register(REQUIRES esp32-camera)

# For manual integration, uncomment and modify the following:
idf_component_register(
    SRCS 
        # Add camera driver source files here when you integrate the library
        "camera_stub.cpp"
    INCLUDE_DIRS 
        "."
        "include"
    REQUIRES 
        driver
        esp_timer
        esp_http_server
)
