idf_component_register(
    SRCS
        "main.cpp"
        "camera_handler.cpp"
        "model_inference.cpp"
        "image_processor.cpp"
    INCLUDE_DIRS
        "."
        "include"
    REQUIRES
        # Core ESP-IDF components
        nvs_flash
        esp_timer
        freertos
        driver
        # Camera component (will be added via dependency or manual integration)
        # esp32-camera
        # TensorFlow Lite Micro (will be integrated separately)
        # tflite-micro
    PRIV_REQUIRES
        # Private requirements
        esp_system
        esp_common
)
