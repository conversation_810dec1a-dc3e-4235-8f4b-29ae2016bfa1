#include "main.h"
#include "camera_handler.h"
#include "model_inference.h"
#include "image_processor.h"

static const char *TAG = "DETECTRA_MAIN";

extern "C" void app_main(void)
{
    ESP_LOGI(TAG, "Starting DETECTRA ESP32-S3 Object Detection System");
    
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // Initialize camera
    ESP_LOGI(TAG, "Initializing camera...");
    ret = camera_handler_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Camera initialization failed: %s", esp_err_to_name(ret));
        return;
    }
    ESP_LOGI(TAG, "Camera initialized successfully");
    
    // Initialize TensorFlow Lite model
    ESP_LOGI(TAG, "Initializing TensorFlow Lite model...");
    ret = model_handler_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Model initialization failed: %s", esp_err_to_name(ret));
        return;
    }
    ESP_LOGI(TAG, "Model initialized successfully");
    
    // Create detection task
    xTaskCreate(detection_task, "detection_task", 8192, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "System initialization complete. Starting object detection...");
}

void detection_task(void *pvParameters)
{
    camera_fb_t *fb = NULL;
    detection_result_t detections[10]; // Max 10 detections
    int num_detections = 0;
    uint8_t *preprocessed_image = NULL;
    
    // Allocate memory for preprocessed image
    preprocessed_image = (uint8_t*)malloc(MODEL_INPUT_WIDTH * MODEL_INPUT_HEIGHT * MODEL_INPUT_CHANNELS);
    if (!preprocessed_image) {
        ESP_LOGE(TAG, "Failed to allocate memory for preprocessed image");
        vTaskDelete(NULL);
        return;
    }
    
    while (1) {
        // Capture frame from camera
        fb = camera_capture_frame();
        if (!fb) {
            ESP_LOGE(TAG, "Camera capture failed");
            vTaskDelay(pdMS_TO_TICKS(100));
            continue;
        }
        
        ESP_LOGI(TAG, "Captured frame: %dx%d, format: %d, len: %d", 
                 fb->width, fb->height, fb->format, fb->len);
        
        // Preprocess image for model input
        esp_err_t ret = preprocess_image_for_model(
            fb->buf, fb->width, fb->height,
            preprocessed_image, MODEL_INPUT_WIDTH, MODEL_INPUT_HEIGHT
        );
        
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Image preprocessing failed: %s", esp_err_to_name(ret));
            camera_return_frame(fb);
            continue;
        }
        
        // Run inference
        int64_t start_time = esp_timer_get_time();
        ret = model_run_inference(preprocessed_image, detections, &num_detections);
        int64_t inference_time = esp_timer_get_time() - start_time;
        
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Model inference failed: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "Inference completed in %lld ms, found %d detections", 
                     inference_time / 1000, num_detections);
            
            // Print detection results
            print_detection_results(detections, num_detections);
        }
        
        // Return frame buffer
        camera_return_frame(fb);
        
        // Small delay to prevent overwhelming the system
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    free(preprocessed_image);
    vTaskDelete(NULL);
}
