#ifndef CAMERA_HANDLER_H
#define CAMERA_HANDLER_H

#include "esp_camera.h"
#include "esp_log.h"
#include "main.h"

// Camera configuration structure
typedef struct {
    camera_config_t config;
    bool initialized;
} camera_handler_t;

// Function declarations
esp_err_t camera_handler_init(void);
camera_fb_t* camera_capture_frame(void);
void camera_return_frame(camera_fb_t* fb);
esp_err_t camera_deinit(void);

// Image preprocessing functions
esp_err_t resize_image(uint8_t* src, int src_width, int src_height, 
                      uint8_t* dst, int dst_width, int dst_height);
esp_err_t convert_rgb565_to_rgb888(uint8_t* src, uint8_t* dst, int width, int height);

#endif // CAMERA_HANDLER_H
